<template>
  <div class="designer-container">
    <!-- 顶部工具栏 -->
    <ToolBar 
      @save="handleSave"
      @load="handleLoad"
      @preview="handlePreview"
      @undo="handleUndo"
      @redo="handleRedo"
    />
    
    <!-- 主要工作区域 -->
    <div class="designer-workspace">
      <!-- 左侧组件库 -->
      <ComponentLibrary 
        class="component-library"
        @component-drag-start="handleComponentDragStart"
      />
      
      <!-- 中间设计画布 -->
      <DesignerCanvas 
        class="designer-canvas"
        :selected-component="selectedComponent"
        :components="canvasComponents"
        @component-select="handleComponentSelect"
        @component-update="handleComponentUpdate"
        @component-delete="handleComponentDelete"
        @canvas-drop="handleCanvasDrop"
      />
      
      <!-- 右侧属性面板 -->
      <PropertyPanel
        class="property-panel"
        :selected-component="selectedComponent"
        @property-change="handlePropertyChange"
        @component-delete="handleComponentDelete"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import ToolBar from '../components/designer/ToolBar.vue'
import ComponentLibrary from '../components/designer/ComponentLibrary.vue'
import DesignerCanvas from '../components/designer/DesignerCanvas.vue'
import PropertyPanel from '../components/designer/PropertyPanel.vue'
import { sampleTemplate, componentDefaults, generateId } from '../utils/test-data.js'

// 响应式数据
const selectedComponent = ref(null)
const canvasComponents = reactive([])
const draggedComponent = ref(null)

// 组件挂载时加载示例数据
onMounted(() => {
  loadSampleData()
})

// 加载示例数据
const loadSampleData = () => {
  canvasComponents.splice(0, canvasComponents.length, ...sampleTemplate.components)
  console.log('已加载示例数据:', canvasComponents.length, '个组件')
}

// 工具栏事件处理
const handleSave = () => {
  console.log('保存模板', canvasComponents)
  // TODO: 实现保存逻辑
}

const handleLoad = () => {
  console.log('加载模板')
  // TODO: 实现加载逻辑
}

const handlePreview = () => {
  console.log('预览模板')
  // TODO: 实现预览逻辑
}

const handleUndo = () => {
  console.log('撤销操作')
  // TODO: 实现撤销逻辑
}

const handleRedo = () => {
  console.log('重做操作')
  // TODO: 实现重做逻辑
}

// 组件库事件处理
const handleComponentDragStart = (componentType) => {
  draggedComponent.value = componentType
  console.log('开始拖拽组件:', componentType)
}

// 画布事件处理
const handleComponentSelect = (component) => {
  selectedComponent.value = component
  console.log('选中组件:', component)
}

const handleComponentUpdate = (componentId, updates) => {
  const component = canvasComponents.find(c => c.id === componentId)
  if (component) {
    Object.assign(component, updates)
  }
  console.log('更新组件:', componentId, updates)
}

const handleComponentDelete = (componentId) => {
  const index = canvasComponents.findIndex(c => c.id === componentId)
  if (index > -1) {
    canvasComponents.splice(index, 1)
    if (selectedComponent.value?.id === componentId) {
      selectedComponent.value = null
    }
  }
  console.log('删除组件:', componentId)
}

const handleCanvasDrop = (dropData) => {
  if (draggedComponent.value) {
    const defaults = componentDefaults[draggedComponent.value] || componentDefaults.text
    const newComponent = {
      id: generateId(),
      type: draggedComponent.value,
      x: dropData.x,
      y: dropData.y,
      width: defaults.width,
      height: defaults.height,
      properties: { ...defaults.properties }
    }
    canvasComponents.push(newComponent)
    selectedComponent.value = newComponent
    draggedComponent.value = null
    console.log('添加新组件:', newComponent)
  }
}

// 属性面板事件处理
const handlePropertyChange = (property, value) => {
  console.log('🎯 DesignerView - handlePropertyChange called:', { property, value })

  if (selectedComponent.value) {
    console.log('📋 Before update - selectedComponent:', {
      id: selectedComponent.value.id,
      type: selectedComponent.value.type,
      properties: { ...selectedComponent.value.properties }
    })

    if (property === 'properties') {
      // 更新整个properties对象
      selectedComponent.value.properties = value
      console.log('🔄 Updated entire properties object')
    } else {
      // 更新基础属性（x、y、width、height等）
      selectedComponent.value[property] = value
      console.log('🔄 Updated basic property:', property)
    }

    console.log('✅ After update - selectedComponent:', {
      id: selectedComponent.value.id,
      type: selectedComponent.value.type,
      properties: { ...selectedComponent.value.properties }
    })

    console.log('属性变更:', property, value)
  } else {
    console.log('❌ No selectedComponent available')
  }
}


</script>

<style scoped>
.designer-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--color-background);
}

.designer-workspace {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.component-library {
  width: 250px;
  border-right: 1px solid var(--color-border);
  background-color: var(--color-background-soft);
}

.designer-canvas {
  flex: 1;
  background-color: var(--color-background);
}

.property-panel {
  width: 300px;
  border-left: 1px solid var(--color-border);
  background-color: var(--color-background-soft);
}
</style>
