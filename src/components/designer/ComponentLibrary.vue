<template>
  <div class="component-library">
    <div class="library-header">
      <h3>组件库</h3>
    </div>

    <div class="library-content">
      <!-- 统一组件分类 -->
      <div class="component-category">
        <h4 class="category-title">组件</h4>
        <div class="component-grid">
          <div
            v-for="component in allComponents"
            :key="component.type"
            class="component-item"
            draggable="true"
            @dragstart="handleDragStart(component.type, $event)"
            @dragend="handleDragEnd"
          >
            <div class="component-icon">
              <component :is="component.icon" />
            </div>
            <span class="component-name">{{ component.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 定义事件
const emit = defineEmits(['component-drag-start'])

// 统一组件列表 - 8个标准组件
const allComponents = ref([
  { type: 'text', name: '文本组件', icon: 'TextIcon' },
  { type: 'date', name: '日期组件', icon: 'DateIcon' },
  { type: 'image', name: '图片组件', icon: 'ImageIcon' },
  { type: 'qrcode', name: '二维码组件', icon: 'QRCodeIcon' },
  { type: 'table', name: '表格组件', icon: 'TableIcon' },
  { type: 'seal', name: '印章组件', icon: 'SealIcon' },
  { type: 'checkbox', name: '复选框组件', icon: 'CheckboxIcon' },
  { type: 'radio', name: '单选框组件', icon: 'RadioIcon' }
])

// 拖拽事件处理
const handleDragStart = (componentType, event) => {
  event.dataTransfer.setData('text/plain', componentType)
  event.dataTransfer.effectAllowed = 'copy'
  emit('component-drag-start', componentType)
}

const handleDragEnd = () => {
  // 拖拽结束处理
}
</script>

<script>
// SVG图标组件
const TextIcon = {
  template: `
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M18.5,4L19.66,8.35L18.7,8.61C18.25,7.74 17.79,7.87 17.26,7.87H15.11L12.5,15.31H14.32C14.95,15.31 15.11,15.17 15.27,14.38L15.53,13.07H16.47L16.08,16.5H7.92L8.31,13.07H9.25L9.5,14.38C9.67,15.17 9.82,15.31 10.45,15.31H12.28L14.89,7.87H12.73C12.11,7.87 11.95,8 11.79,8.8L11.53,10.1H10.59L11,4H18.5Z"/>
    </svg>
  `
}

const ImageIcon = {
  template: `
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"/>
    </svg>
  `
}



const TableIcon = {
  template: `
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M5,4H19A2,2 0 0,1 21,6V18A2,2 0 0,1 19,20H5A2,2 0 0,1 3,18V6A2,2 0 0,1 5,4M5,8V12H11V8H5M13,8V12H19V8H13M5,14V18H11V14H5M13,14V18H19V14H13Z"/>
    </svg>
  `
}



const QRCodeIcon = {
  template: `
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M3,11H5V13H3V11M11,5H13V9H11V5M9,11H13V15H11V13H9V11M15,11H17V13H19V11H21V13H19V15H21V19H19V21H17V19H13V21H11V19H13V17H15V15H17V13H15V11M1,5H9V13H1V5M3,7V11H7V7H3M15,3H23V11H15V3M17,5V9H21V5H17M1,15H9V23H1V15M3,17V21H7V17H3Z"/>
    </svg>
  `
}

const DateIcon = {
  template: `
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M19,3H18V1H16V3H8V1H6V3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19Z"/>
    </svg>
  `
}

const SealIcon = {
  template: `
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z"/>
    </svg>
  `
}

const CheckboxIcon = {
  template: `
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M10,17L5,12L6.41,10.58L10,14.17L17.59,6.58L19,8M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3Z"/>
    </svg>
  `
}

const RadioIcon = {
  template: `
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z"/>
    </svg>
  `
}

export default {
  components: {
    TextIcon,
    ImageIcon,
    TableIcon,
    QRCodeIcon,
    DateIcon,
    SealIcon,
    CheckboxIcon,
    RadioIcon
  }
}
</script>

<style scoped>
.component-library {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.library-header {
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
}

.library-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-heading);
}

.library-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.component-category {
  margin-bottom: 24px;
}

.category-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text);
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--color-border);
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background-color: var(--color-background);
  cursor: grab;
  transition: all 0.2s ease;
  user-select: none;
}

.component-item:hover {
  background-color: var(--color-background-mute);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.component-item:active {
  cursor: grabbing;
  transform: translateY(0);
}

.component-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-bottom: 6px;
  color: var(--color-text);
}

.component-name {
  font-size: 12px;
  color: var(--color-text);
  text-align: center;
  line-height: 1.2;
}

/* 拖拽时的样式 */
.component-item.dragging {
  opacity: 0.5;
}

/* 滚动条样式 */
.library-content::-webkit-scrollbar {
  width: 6px;
}

.library-content::-webkit-scrollbar-track {
  background: var(--color-background-soft);
}

.library-content::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.library-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}
</style>
