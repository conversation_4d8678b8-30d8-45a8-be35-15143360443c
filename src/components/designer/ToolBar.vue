<template>
  <div class="toolbar">
    <div class="toolbar-section">
      <h1 class="app-title">OFD模板设计器</h1>
    </div>

    <div class="toolbar-section toolbar-actions">
      <el-button
        type="primary"
        @click="$emit('save')"
        :icon="DocumentAdd"
        size="default"
      >
        保存
      </el-button>

      <el-button
        type="primary"
        @click="$emit('load')"
        :icon="FolderOpened"
        size="default"
      >
        加载
      </el-button>

      <el-divider direction="vertical" />

      <el-button
        type="primary"
        @click="$emit('undo')"
        :icon="RefreshLeft"
        size="default"
      >
        撤销
      </el-button>

      <el-button
        type="primary"
        @click="$emit('redo')"
        :icon="RefreshRight"
        size="default"
      >
        重做
      </el-button>

      <el-divider direction="vertical" />

      <el-button
        type="primary"
        @click="$emit('preview')"
        :icon="View"
        size="default"
        class="preview-btn"
      >
        预览
      </el-button>
    </div>
  </div>
</template>

<script setup>
import {
  DocumentAdd,
  FolderOpened,
  RefreshLeft,
  RefreshRight,
  View
} from '@element-plus/icons-vue'

// 定义事件
defineEmits(['save', 'load', 'preview', 'undo', 'redo'])
</script>

<style scoped>
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 20px;
  background-color: var(--theme-primary);
  border-bottom: 1px solid var(--color-border);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--theme-text-white);
  margin: 0;
}

.toolbar-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Element Plus 按钮自定义样式 */
:deep(.el-button--primary) {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: var(--theme-text-white);
}

:deep(.el-button--primary:hover) {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
  color: var(--theme-text-white);
}

:deep(.el-button--primary:active) {
  background-color: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.5);
}

:deep(.el-button--primary.is-disabled) {
  background-color: var(--theme-primary-disabled);
  border-color: var(--theme-primary-disabled);
  color: var(--theme-text-disabled);
}

.preview-btn:deep(.el-button--primary) {
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--theme-primary);
  font-weight: 600;
}

.preview-btn:deep(.el-button--primary:hover) {
  background-color: var(--theme-text-white);
  color: var(--theme-primary-hover);
}

/* 分割线样式 */
:deep(.el-divider--vertical) {
  height: 20px;
  border-color: rgba(255, 255, 255, 0.3);
  margin: 0 8px;
}

@media (max-width: 768px) {
  .toolbar-actions {
    gap: 4px;
  }

  :deep(.el-button span) {
    display: none;
  }

  :deep(.el-divider--vertical) {
    margin: 0 4px;
  }
}
</style>
