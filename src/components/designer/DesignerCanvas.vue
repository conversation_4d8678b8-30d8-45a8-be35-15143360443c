<template>
  <div class="designer-canvas">
    <div class="canvas-header">
      <div class="canvas-info">
        <span>画布: 210mm × 297mm (A4)</span>
        <span class="zoom-info">缩放: {{ Math.round(zoomLevel * 100) }}%</span>
      </div>
      <div class="canvas-controls">
        <button @click="zoomOut" class="zoom-btn">-</button>
        <button @click="resetZoom" class="zoom-btn">100%</button>
        <button @click="zoomIn" class="zoom-btn">+</button>
      </div>
    </div>
    
    <div 
      class="canvas-container"
      @wheel="handleWheel"
      @mousedown="handleCanvasMouseDown"
    >
      <div 
        class="canvas-viewport"
        :style="{ transform: `scale(${zoomLevel})` }"
      >
        <div 
          class="canvas-paper"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @click="handleCanvasClick"
        >
          <!-- 网格背景 -->
          <div class="canvas-grid"></div>
          
          <!-- 渲染组件 -->
          <div
            v-for="component in components"
            :key="component.id"
            class="canvas-component"
            :class="{ 'selected': selectedComponent?.id === component.id }"
            :style="{
              left: component.x + 'px',
              top: component.y + 'px',
              width: component.width + 'px',
              height: component.height + 'px'
            }"
            @click.stop="selectComponent(component)"
            @mousedown="startDrag(component, $event)"
          >
            <!-- 组件内容 -->
            <component 
              :is="getComponentType(component.type)"
              :component-data="component"
              @update="updateComponent"
            />
            
            <!-- 选中状态的控制点 -->
            <div v-if="selectedComponent?.id === component.id" class="selection-handles">
              <div class="handle handle-nw" @mousedown.stop="startResize(component, 'nw', $event)"></div>
              <div class="handle handle-ne" @mousedown.stop="startResize(component, 'ne', $event)"></div>
              <div class="handle handle-sw" @mousedown.stop="startResize(component, 'sw', $event)"></div>
              <div class="handle handle-se" @mousedown.stop="startResize(component, 'se', $event)"></div>
            </div>
            
            <!-- 删除按钮 -->
            <button 
              v-if="selectedComponent?.id === component.id"
              class="delete-btn"
              @click.stop="deleteComponent(component.id)"
            >
              ×
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import TextComponent from '../ofd-components/TextComponent.vue'
import DateComponent from '../ofd-components/DateComponent.vue'
import ImageComponent from '../ofd-components/ImageComponent.vue'
import QRCodeComponent from '../ofd-components/QRCodeComponent.vue'
import TableComponent from '../ofd-components/TableComponent.vue'
import SealComponent from '../ofd-components/SealComponent.vue'
import CheckboxComponent from '../ofd-components/CheckboxComponent.vue'
import RadioComponent from '../ofd-components/RadioComponent.vue'

// Props
const props = defineProps({
  selectedComponent: Object,
  components: Array
})

// Emits
const emit = defineEmits(['component-select', 'component-update', 'component-delete', 'canvas-drop'])

// 响应式数据
const zoomLevel = ref(1)
const isDragging = ref(false)
const isResizing = ref(false)
const dragData = ref(null)

// 缩放控制
const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.1, 3)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.1)
}

const resetZoom = () => {
  zoomLevel.value = 1
}

const handleWheel = (event) => {
  if (event.ctrlKey) {
    event.preventDefault()
    const delta = event.deltaY > 0 ? -0.05 : 0.05
    zoomLevel.value = Math.max(0.1, Math.min(3, zoomLevel.value + delta))
  }
}

// 组件类型映射
const getComponentType = (type) => {
  const componentMap = {
    text: TextComponent,
    date: DateComponent,
    image: ImageComponent,
    qrcode: QRCodeComponent,
    table: TableComponent,
    seal: SealComponent,
    checkbox: CheckboxComponent,
    radio: RadioComponent
  }
  return componentMap[type] || TextComponent
}

// 画布事件处理
const handleCanvasClick = () => {
  emit('component-select', null)
}

const handleCanvasMouseDown = (event) => {
  if (event.target.classList.contains('canvas-paper')) {
    emit('component-select', null)
  }
}

// 拖放处理
const handleDragOver = (event) => {
  event.preventDefault()
  event.dataTransfer.dropEffect = 'copy'
}

const handleDrop = (event) => {
  event.preventDefault()
  const componentType = event.dataTransfer.getData('text/plain')
  if (componentType) {
    const rect = event.currentTarget.getBoundingClientRect()
    const x = (event.clientX - rect.left) / zoomLevel.value
    const y = (event.clientY - rect.top) / zoomLevel.value
    
    emit('canvas-drop', { x, y, componentType })
  }
}

// 组件选择
const selectComponent = (component) => {
  emit('component-select', component)
}

// 组件拖拽
const startDrag = (component, event) => {
  if (event.target.classList.contains('handle') || event.target.classList.contains('delete-btn')) {
    return
  }
  
  isDragging.value = true
  const startX = event.clientX
  const startY = event.clientY
  const startComponentX = component.x
  const startComponentY = component.y
  
  const handleMouseMove = (e) => {
    const deltaX = (e.clientX - startX) / zoomLevel.value
    const deltaY = (e.clientY - startY) / zoomLevel.value
    
    updateComponent(component.id, {
      x: startComponentX + deltaX,
      y: startComponentY + deltaY
    })
  }
  
  const handleMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 组件缩放
const startResize = (component, direction, event) => {
  isResizing.value = true
  const startX = event.clientX
  const startY = event.clientY
  const startWidth = component.width
  const startHeight = component.height
  const startComponentX = component.x
  const startComponentY = component.y
  
  const handleMouseMove = (e) => {
    const deltaX = (e.clientX - startX) / zoomLevel.value
    const deltaY = (e.clientY - startY) / zoomLevel.value
    
    let newWidth = startWidth
    let newHeight = startHeight
    let newX = startComponentX
    let newY = startComponentY
    
    if (direction.includes('e')) {
      newWidth = Math.max(20, startWidth + deltaX)
    }
    if (direction.includes('w')) {
      newWidth = Math.max(20, startWidth - deltaX)
      newX = startComponentX + deltaX
    }
    if (direction.includes('s')) {
      newHeight = Math.max(20, startHeight + deltaY)
    }
    if (direction.includes('n')) {
      newHeight = Math.max(20, startHeight - deltaY)
      newY = startComponentY + deltaY
    }
    
    updateComponent(component.id, {
      x: newX,
      y: newY,
      width: newWidth,
      height: newHeight
    })
  }
  
  const handleMouseUp = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 更新组件
const updateComponent = (componentId, updates) => {
  emit('component-update', componentId, updates)
}

// 删除组件
const deleteComponent = (componentId) => {
  emit('component-delete', componentId)
}
</script>

<style scoped>
.designer-canvas {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--color-background-soft);
  border-bottom: 1px solid var(--color-border);
}

.canvas-info {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: var(--color-text);
}

.canvas-controls {
  display: flex;
  gap: 4px;
}

.zoom-btn {
  padding: 4px 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background-color: var(--color-background);
  color: var(--color-text);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.zoom-btn:hover {
  background-color: var(--color-background-mute);
}

.canvas-container {
  flex: 1;
  overflow: auto;
  position: relative;
  background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
              linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
              linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.canvas-viewport {
  transform-origin: top left;
  transition: transform 0.1s ease;
}

.canvas-paper {
  width: 794px; /* A4 width at 96 DPI */
  height: 1123px; /* A4 height at 96 DPI */
  background-color: white;
  margin: 50px auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.canvas-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
}

.canvas-component {
  position: absolute;
  border: 1px solid transparent;
  cursor: move;
  user-select: none;
}

.canvas-component.selected {
  border-color: #007bff;
  box-shadow: 0 0 0 1px #007bff;
}

.selection-handles {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  pointer-events: none;
}

.handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #007bff;
  border: 1px solid white;
  border-radius: 50%;
  pointer-events: all;
  cursor: pointer;
}

.handle-nw { top: 0; left: 0; cursor: nw-resize; }
.handle-ne { top: 0; right: 0; cursor: ne-resize; }
.handle-sw { bottom: 0; left: 0; cursor: sw-resize; }
.handle-se { bottom: 0; right: 0; cursor: se-resize; }

.delete-btn {
  position: absolute;
  top: -12px;
  right: -12px;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 50%;
  background-color: #dc3545;
  color: white;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.delete-btn:hover {
  background-color: #c82333;
}
</style>
