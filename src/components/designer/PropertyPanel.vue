<template>
  <div class="property-panel">
    <!-- 顶部标签页 -->
    <div class="panel-tabs">
      <div class="tab-item active">样式</div>
      <div class="tab-item">数据</div>
    </div>

    <div class="panel-content">
      <div v-if="!selectedComponent" class="no-selection">
        <div class="no-selection-icon">📝</div>
        <p>请选择一个组件来编辑属性</p>
      </div>

      <div v-else class="property-sections">
        <!-- 组件标题和移除按钮 -->
        <div class="component-header-simple">
          <h3 class="component-title-simple">{{ getComponentTypeName(selectedComponent.type) }}</h3>
          <button class="remove-btn-simple" @click="removeComponent">移除</button>
        </div>
        <!-- 基本属性 -->
        <div class="property-group">
          <h4 class="group-title">基本属性</h4>
          <div class="property-grid">
            <!-- 第1行：x坐标 | y坐标 -->
            <div class="property-row">
              <div class="property-item-horizontal">
                <label class="property-label-left">x:</label>
                <div class="input-with-unit">
                  <el-input-number
                    :model-value="Math.round(selectedComponent.x)"
                    @change="updateProperty('x', $event)"
                    :min="0"
                    :max="2000"
                    :step="1"
                    size="small"
                    :controls="false"
                    class="property-input-numeric"
                  />
                  <span class="unit-text">px</span>
                </div>
              </div>
              <div class="property-item-horizontal">
                <label class="property-label-left">y:</label>
                <div class="input-with-unit">
                  <el-input-number
                    :model-value="Math.round(selectedComponent.y)"
                    @change="updateProperty('y', $event)"
                    :min="0"
                    :max="2000"
                    :step="1"
                    size="small"
                    :controls="false"
                    class="property-input-numeric"
                  />
                  <span class="unit-text">px</span>
                </div>
              </div>
            </div>

            <!-- 第2行：w（宽度）| h（高度） -->
            <div class="property-row">
              <div class="property-item-horizontal">
                <label class="property-label-left">w:</label>
                <div class="input-with-unit">
                  <el-input-number
                    :model-value="Math.round(selectedComponent.width)"
                    @change="updateProperty('width', $event)"
                    :min="1"
                    :max="2000"
                    :step="1"
                    size="small"
                    :controls="false"
                    class="property-input-numeric"
                  />
                  <span class="unit-text">px</span>
                </div>
              </div>
              <div class="property-item-horizontal">
                <label class="property-label-left">h:</label>
                <div class="input-with-unit">
                  <el-input-number
                    :model-value="Math.round(selectedComponent.height)"
                    @change="updateProperty('height', $event)"
                    :min="1"
                    :max="2000"
                    :step="1"
                    size="small"
                    :controls="false"
                    class="property-input-numeric"
                  />
                  <span class="unit-text">px</span>
                </div>
              </div>
            </div>

            <!-- 第3行：旋转 | 透明度 -->
            <div class="property-row">
              <div class="property-item-horizontal">
                <label class="property-label-left">旋转:</label>
                <div class="input-with-unit">
                  <el-input-number
                    :model-value="selectedComponent.properties?.rotation || 0"
                    @change="updateComponentProperty('rotation', $event)"
                    :min="-360"
                    :max="360"
                    :step="1"
                    size="small"
                    :controls="false"
                    class="property-input-numeric"
                  />
                  <span class="unit-text">°</span>
                </div>
              </div>
              <div class="property-item-horizontal">
                <label class="property-label-left">透明度:</label>
                <div class="input-with-unit">
                  <el-input-number
                    :model-value="selectedComponent.properties?.opacity || 100"
                    @change="updateComponentProperty('opacity', $event)"
                    :min="0"
                    :max="100"
                    :step="1"
                    size="small"
                    :controls="false"
                    class="property-input-numeric"
                  />
                  <span class="unit-text">%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 组件绑定数据项短名 -->
          <div class="property-item-full">
            <label class="property-label-with-icon">
              组件绑定数据项短名
              <el-icon class="help-icon"><QuestionFilled /></el-icon>
            </label>
            <el-input
              :model-value="selectedComponent.properties?.dataBinding || ''"
              @input="updateComponentProperty('dataBinding', $event)"
              placeholder=""
              size="small"
              class="property-input-full"
            />
          </div>

          <!-- 隐藏组件 -->
          <div class="property-item-checkbox">
            <el-checkbox
              :model-value="selectedComponent.properties?.hidden || false"
              @change="updateComponentProperty('hidden', $event)"
              size="small"
            >
              隐藏组件
              <el-icon class="help-icon"><QuestionFilled /></el-icon>
            </el-checkbox>
          </div>
        </div>
        
        <!-- 组件特定属性 -->
        <div class="property-group">
          <h4 class="group-title">组件属性</h4>
          <el-form
            :model="componentFormData"
            label-width="80px"
            size="small"
            :disabled="formLoading"
          >
            <!-- 文本组件属性 -->
            <template v-if="selectedComponent.type === 'text'">
              <!-- 文字段落 -->
              <div class="property-group">
                <h4 class="group-title">文字段落</h4>

                <div class="property-grid">
                  <!-- 第1行：字体 | 字号 -->
                  <div class="property-row">
                    <div class="property-item-horizontal">
                      <label class="property-label-left">字体:</label>
                      <el-select
                        :model-value="currentTextProperties.fontFamily"
                        @change="updateTextComponentProperty('fontFamily', $event)"
                        placeholder="宋体"
                        size="small"
                        class="property-select-compact"
                      >
                        <el-option
                          v-for="font in fontOptions"
                          :key="font.value"
                          :label="font.label"
                          :value="font.value"
                        />
                      </el-select>
                    </div>
                    <div class="property-item-horizontal">
                      <label class="property-label-left">字号:</label>
                      <div class="input-with-unit">
                        <el-input-number
                          :model-value="currentTextProperties.fontSize"
                          @change="updateTextComponentProperty('fontSize', $event)"
                          :min="8"
                          :max="72"
                          :step="1"
                          size="small"
                          :controls="false"
                          class="property-input-numeric"
                        />
                        <span class="unit-text">px</span>
                      </div>
                    </div>
                  </div>

                  <!-- 第2行：倍数 | 行间距 -->
                  <div class="property-row">
                    <div class="property-item-horizontal">
                      <label class="property-label-left">倍数:</label>
                      <div class="input-with-unit">
                        <el-input-number
                          :model-value="currentTextProperties.fontScale"
                          @change="updateTextComponentProperty('fontScale', $event)"
                          :min="0.1"
                          :max="5"
                          :step="0.1"
                          :precision="1"
                          size="small"
                          :controls="false"
                          class="property-input-numeric"
                        />
                      </div>
                    </div>
                    <div class="property-item-horizontal">
                      <label class="property-label-left">行间距:</label>
                      <div class="input-with-unit">
                        <el-input-number
                          :model-value="currentTextProperties.lineHeight"
                          @change="updateTextComponentProperty('lineHeight', $event)"
                          :min="0.5"
                          :max="5"
                          :step="0.1"
                          :precision="1"
                          size="small"
                          :controls="false"
                          class="property-input-numeric"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- 第3行：字间距 | 字体样式按钮 -->
                  <div class="property-row">
                    <div class="property-item-horizontal">
                      <label class="property-label-left">字间距:</label>
                      <div class="input-with-unit">
                        <el-input-number
                          :model-value="currentTextProperties.letterSpacing"
                          @change="updateTextComponentProperty('letterSpacing', $event)"
                          :min="-10"
                          :max="20"
                          :step="0.1"
                          :precision="1"
                          size="small"
                          :controls="false"
                          class="property-input-numeric"
                        />
                        <span class="unit-text">px</span>
                      </div>
                    </div>
                    <div class="property-item-horizontal">
                      <label class="property-label-left">样式:</label>
                      <div class="font-style-buttons">
                        <button
                          type="button"
                          class="style-btn"
                          :class="{ active: currentTextProperties.fontWeight === 'bold' }"
                          @click="toggleFontWeight($event)"
                          title="粗体"
                        >
                          <strong>A</strong>
                        </button>
                        <button
                          type="button"
                          class="style-btn"
                          :class="{ active: currentTextProperties.fontStyle === 'italic' }"
                          @click="toggleFontStyle($event)"
                          title="斜体"
                        >
                          <em>I</em>
                        </button>
                        <button
                          type="button"
                          class="style-btn"
                          :class="{ active: currentTextProperties.textDecoration === 'underline' }"
                          @click="toggleTextDecoration($event)"
                          title="下划线"
                        >
                          <u>U</u>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 对齐方式 -->
                <div class="property-item-full">
                  <label class="property-label">对齐</label>
                  <div class="align-buttons">
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.textAlign === 'left' }"
                      @click="updateTextComponentProperty('textAlign', 'left', $event)"
                      title="左对齐"
                    >
                      ≡
                    </button>
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.textAlign === 'center' }"
                      @click="updateTextComponentProperty('textAlign', 'center', $event)"
                      title="居中对齐"
                    >
                      ≣
                    </button>
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.textAlign === 'right' }"
                      @click="updateTextComponentProperty('textAlign', 'right', $event)"
                      title="右对齐"
                    >
                      ≡
                    </button>
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.textAlign === 'justify' }"
                      @click="updateTextComponentProperty('textAlign', 'justify', $event)"
                      title="两端对齐"
                    >
                      ≣
                    </button>
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.verticalAlign === 'top' }"
                      @click="updateTextComponentProperty('verticalAlign', 'top', $event)"
                      title="顶部对齐"
                    >
                      ⤴
                    </button>
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.verticalAlign === 'middle' }"
                      @click="updateTextComponentProperty('verticalAlign', 'middle', $event)"
                      title="垂直居中"
                    >
                      ↕
                    </button>
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.verticalAlign === 'bottom' }"
                      @click="updateTextComponentProperty('verticalAlign', 'bottom', $event)"
                      title="底部对齐"
                    >
                      ⤵
                    </button>
                  </div>
                </div>

                <!-- 第4行：换行处理 | 西文换行 -->
                <div class="property-row">
                  <div class="property-item-horizontal">
                    <label class="property-label-left">换行:</label>
                    <el-select
                      :model-value="currentTextProperties.whiteSpace"
                      @change="updateTextComponentProperty('whiteSpace', $event)"
                      size="small"
                      class="property-select-compact"
                    >
                      <el-option label="自动换行" value="normal" />
                      <el-option label="不换行" value="nowrap" />
                      <el-option label="保留空格" value="pre" />
                      <el-option label="保留换行" value="pre-line" />
                    </el-select>
                  </div>
                  <div class="property-item-horizontal">
                    <label class="property-label-left">西文:</label>
                    <el-checkbox
                      :model-value="currentTextProperties.wordBreak"
                      @change="updateTextComponentProperty('wordBreak', $event)"
                      size="small"
                    >
                      单词中换行
                    </el-checkbox>
                  </div>
                </div>
              </div>

              <!-- 内容 -->
              <div class="property-group">
                <h4 class="group-title">内容</h4>

                <!-- 大写数字 -->
                <div class="property-item-checkbox">
                  <el-checkbox
                    :model-value="currentTextProperties.upperCase"
                    @change="updateTextComponentProperty('upperCase', $event)"
                    size="small"
                  >
                    大写数字
                  </el-checkbox>
                </div>

                <!-- 文本内容 -->
                <div class="property-item-full">
                  <el-input
                    v-model="textContent"
                    @input="handleTextContentChange"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入文本内容"
                    size="small"
                    class="property-textarea"
                  />
                </div>
              </div>
            </template>
            
            <!-- 图片组件属性 -->
            <template v-if="selectedComponent.type === 'image'">
              <el-form-item
                label="底色透明处理"
                prop="transparentBackground"
              >
                <el-radio-group
                  :model-value="selectedComponent.properties.dataProperties?.transparentBackground || selectedComponent.properties.transparentBackground || 'disabled'"
                  @change="updateComponentDataProperty('transparentBackground', $event)"
                  size="small"
                >
                  <el-radio value="enabled">启用</el-radio>
                  <el-radio value="disabled">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </template>
            
            <!-- 形状组件属性 -->
            <template v-if="selectedComponent.type === 'shape'">
              <el-form-item
                label="形状类型"
                prop="shapeType"
                :rules="[{ required: true, message: '请选择形状类型', trigger: 'change' }]"
              >
                <el-select
                  :model-value="selectedComponent.properties.dataProperties?.shapeType || selectedComponent.properties.shapeType"
                  @change="updateComponentDataProperty('shapeType', $event)"
                  placeholder="请选择形状（仅数据记录）"
                  size="small"
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="shape in shapeOptions"
                    :key="shape.value"
                    :label="shape.label"
                    :value="shape.value"
                  >
                    <span style="float: left">{{ shape.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ shape.icon }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="填充颜色" prop="fillColor">
                    <el-color-picker
                      :model-value="selectedComponent.properties.dataProperties?.fillColor || selectedComponent.properties.fillColor"
                      @change="updateComponentDataProperty('fillColor', $event)"
                      show-alpha
                      :predefine="predefineColors"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="边框颜色" prop="borderColor">
                    <el-color-picker
                      :model-value="selectedComponent.properties.dataProperties?.borderColor || selectedComponent.properties.borderColor"
                      @change="updateComponentDataProperty('borderColor', $event)"
                      show-alpha
                      :predefine="predefineColors"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item
                label="边框宽度"
                prop="borderWidth"
                :rules="[{ required: true, message: '边框宽度不能为空', trigger: 'blur' }]"
              >
                <el-input-number
                  :model-value="selectedComponent.properties.dataProperties?.borderWidth || selectedComponent.properties.borderWidth"
                  @change="updateComponentDataProperty('borderWidth', $event)"
                  :min="0"
                  :max="10"
                  :step="1"
                  size="small"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </template>
            
            <!-- 表格组件属性 -->
            <template v-if="selectedComponent.type === 'table'">
              <!-- 文字段落 -->
              <div class="property-group">
                <h4 class="group-title">文字段落</h4>

                <div class="property-grid">
                  <!-- 第1行：字体 | 字号 -->
                  <div class="property-row">
                    <div class="property-item-horizontal">
                      <label class="property-label-left">字体:</label>
                      <el-select
                        :model-value="currentTextProperties.fontFamily"
                        @change="updateTextComponentProperty('fontFamily', $event)"
                        placeholder="宋体"
                        size="small"
                        class="property-select-compact"
                      >
                        <el-option
                          v-for="font in fontOptions"
                          :key="font.value"
                          :label="font.label"
                          :value="font.value"
                        />
                      </el-select>
                    </div>
                    <div class="property-item-horizontal">
                      <label class="property-label-left">字号:</label>
                      <div class="input-with-unit">
                        <el-input-number
                          :model-value="currentTextProperties.fontSize"
                          @change="updateTextComponentProperty('fontSize', $event)"
                          :min="8"
                          :max="72"
                          :step="1"
                          size="small"
                          :controls="false"
                          class="property-input-numeric"
                        />
                        <span class="unit-text">px</span>
                      </div>
                    </div>
                  </div>

                  <!-- 第2行：倍数 | 行间距 -->
                  <div class="property-row">
                    <div class="property-item-horizontal">
                      <label class="property-label-left">倍数:</label>
                      <div class="input-with-unit">
                        <el-input-number
                          :model-value="currentTextProperties.fontScale"
                          @change="updateTextComponentProperty('fontScale', $event)"
                          :min="0.1"
                          :max="5"
                          :step="0.1"
                          :precision="1"
                          size="small"
                          :controls="false"
                          class="property-input-numeric"
                        />
                      </div>
                    </div>
                    <div class="property-item-horizontal">
                      <label class="property-label-left">行间距:</label>
                      <div class="input-with-unit">
                        <el-input-number
                          :model-value="currentTextProperties.lineHeight"
                          @change="updateTextComponentProperty('lineHeight', $event)"
                          :min="0.5"
                          :max="5"
                          :step="0.1"
                          :precision="1"
                          size="small"
                          :controls="false"
                          class="property-input-numeric"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- 第3行：字间距 | 字体样式按钮 -->
                  <div class="property-row">
                    <div class="property-item-horizontal">
                      <label class="property-label-left">字间距:</label>
                      <div class="input-with-unit">
                        <el-input-number
                          :model-value="currentTextProperties.letterSpacing"
                          @change="updateTextComponentProperty('letterSpacing', $event)"
                          :min="-10"
                          :max="20"
                          :step="0.1"
                          :precision="1"
                          size="small"
                          :controls="false"
                          class="property-input-numeric"
                        />
                        <span class="unit-text">px</span>
                      </div>
                    </div>
                    <div class="property-item-horizontal">
                      <label class="property-label-left">样式:</label>
                      <div class="font-style-buttons">
                        <button
                          type="button"
                          class="style-btn"
                          :class="{ active: currentTextProperties.fontWeight === 'bold' }"
                          @click="toggleFontWeight($event)"
                          title="粗体"
                        >
                          <strong>A</strong>
                        </button>
                        <button
                          type="button"
                          class="style-btn"
                          :class="{ active: currentTextProperties.fontStyle === 'italic' }"
                          @click="toggleFontStyle($event)"
                          title="斜体"
                        >
                          <em>I</em>
                        </button>
                        <button
                          type="button"
                          class="style-btn"
                          :class="{ active: currentTextProperties.textDecoration === 'underline' }"
                          @click="toggleTextDecoration($event)"
                          title="下划线"
                        >
                          <u>U</u>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 对齐方式 -->
                <div class="property-item-full">
                  <label class="property-label">对齐</label>
                  <div class="align-buttons">
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.textAlign === 'left' }"
                      @click="updateTextComponentProperty('textAlign', 'left', $event)"
                      title="左对齐"
                    >
                      ≡
                    </button>
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.textAlign === 'center' }"
                      @click="updateTextComponentProperty('textAlign', 'center', $event)"
                      title="居中对齐"
                    >
                      ≣
                    </button>
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.textAlign === 'right' }"
                      @click="updateTextComponentProperty('textAlign', 'right', $event)"
                      title="右对齐"
                    >
                      ≡
                    </button>
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.textAlign === 'justify' }"
                      @click="updateTextComponentProperty('textAlign', 'justify', $event)"
                      title="两端对齐"
                    >
                      ≣
                    </button>
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.verticalAlign === 'top' }"
                      @click="updateTextComponentProperty('verticalAlign', 'top', $event)"
                      title="顶部对齐"
                    >
                      ⤴
                    </button>
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.verticalAlign === 'middle' }"
                      @click="updateTextComponentProperty('verticalAlign', 'middle', $event)"
                      title="垂直居中"
                    >
                      ↕
                    </button>
                    <button
                      type="button"
                      class="align-btn"
                      :class="{ active: currentTextProperties.verticalAlign === 'bottom' }"
                      @click="updateTextComponentProperty('verticalAlign', 'bottom', $event)"
                      title="底部对齐"
                    >
                      ⤵
                    </button>
                  </div>
                </div>

                <!-- 第4行：换行处理 | 西文换行 -->
                <div class="property-row">
                  <div class="property-item-horizontal">
                    <label class="property-label-left">换行:</label>
                    <el-select
                      :model-value="currentTextProperties.whiteSpace"
                      @change="updateTextComponentProperty('whiteSpace', $event)"
                      size="small"
                      class="property-select-compact"
                    >
                      <el-option label="自动换行" value="normal" />
                      <el-option label="不换行" value="nowrap" />
                      <el-option label="保留空格" value="pre" />
                      <el-option label="保留换行" value="pre-line" />
                    </el-select>
                  </div>
                  <div class="property-item-horizontal">
                    <label class="property-label-left">西文:</label>
                    <el-checkbox
                      :model-value="currentTextProperties.wordBreak"
                      @change="updateTextComponentProperty('wordBreak', $event)"
                      size="small"
                    >
                      单词中换行
                    </el-checkbox>
                  </div>
                </div>
              </div>

              <!-- 分割线 -->
              <div style="border-top: 1px solid #e4e7ed; margin: 20px 0;"></div>

              <!-- 表格属性组 -->
              <div class="property-group">
                <h4 class="group-title">表格</h4>

                <div class="property-grid">
                  <!-- 行列配置 -->
                  <div class="property-row">
                    <div class="property-item-horizontal">
                      <label class="property-label-left">列数:</label>
                      <div class="input-with-unit">
                        <el-input-number
                          :model-value="selectedComponent?.properties?.dataProperties?.cols || selectedComponent?.properties?.cols || 3"
                          @change="updateComponentDataProperty('cols', $event)"
                          :min="1"
                          :max="10"
                          :step="1"
                          size="small"
                          :controls="false"
                          class="property-input-numeric"
                        />
                      </div>
                    </div>
                    <div class="property-item-horizontal">
                      <label class="property-label-left">行数:</label>
                      <div class="input-with-unit">
                        <el-input-number
                          :model-value="selectedComponent?.properties?.dataProperties?.rows || selectedComponent?.properties?.rows || 3"
                          @change="updateComponentDataProperty('rows', $event)"
                          :min="1"
                          :max="20"
                          :step="1"
                          size="small"
                          :controls="false"
                          class="property-input-numeric"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- 提示文字 -->
                  <div class="property-item-full">
                    <p class="property-hint" style="color: #909399; font-size: 12px; margin: 8px 0; line-height: 1.4;">
                      行数为空，组件将根据内容动态调整行数
                    </p>
                  </div>

                  <!-- 表格选项 -->
                  <div class="property-row">
                    <div class="property-item-full">
                      <div style="display: flex; flex-direction: column; gap: 8px;">
                        <el-checkbox
                          :model-value="selectedComponent?.properties?.dataProperties?.boldHeader || selectedComponent?.properties?.boldHeader || false"
                          @change="updateComponentDataProperty('boldHeader', $event)"
                          size="small"
                        >
                          表头内容加粗
                        </el-checkbox>
                        <el-checkbox
                          :model-value="selectedComponent?.properties?.dataProperties?.showHeader || selectedComponent?.properties?.showHeader || false"
                          @change="updateComponentDataProperty('showHeader', $event)"
                          size="small"
                        >
                          显示表头
                        </el-checkbox>
                        <el-checkbox
                          :model-value="selectedComponent?.properties?.dataProperties?.autoPage || selectedComponent?.properties?.autoPage || false"
                          @change="updateComponentDataProperty('autoPage', $event)"
                          size="small"
                        >
                          表格内容超出当前页面时，自动生成下一页
                        </el-checkbox>
                      </div>
                    </div>
                  </div>

                  <!-- 边框设置 -->
                  <div class="property-row">
                    <div class="property-item-horizontal">
                      <label class="property-label-left">边框颜色:</label>
                      <el-color-picker
                        :model-value="selectedComponent?.properties?.dataProperties?.borderColor || selectedComponent?.properties?.borderColor || '#000000'"
                        @change="updateComponentDataProperty('borderColor', $event)"
                        show-alpha
                        size="small"
                      />
                    </div>
                    <div class="property-item-horizontal">
                      <label class="property-label-left">线宽:</label>
                      <div class="input-with-unit">
                        <el-input-number
                          :model-value="selectedComponent?.properties?.dataProperties?.borderWidth || selectedComponent?.properties?.borderWidth || 1"
                          @change="updateComponentDataProperty('borderWidth', $event)"
                          :min="0"
                          :max="5"
                          :step="1"
                          size="small"
                          :controls="false"
                          class="property-input-numeric"
                        />
                        <span class="unit-text">px</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>

            <!-- 日期组件属性 -->
            <template v-if="selectedComponent.type === 'date'">
              <div class="property-group">
                <div class="property-grid">
                  <!-- 第1行：字体 | 字号 -->
                  <div class="property-row">
                    <div class="property-item-horizontal">
                      <label class="property-label-left">字体:</label>
                      <el-select
                          :model-value="currentDateProperties.fontFamily"
                          @change="updateDateComponentProperty('fontFamily', $event)"
                          placeholder="宋体"
                          size="small"
                          class="property-select-compact"
                      >
                        <el-option
                            v-for="font in fontOptions"
                            :key="font.value"
                            :label="font.label"
                            :value="font.value"
                        />
                      </el-select>
                    </div>
                    <div class="property-item-horizontal">
                      <label class="property-label-left">字号:</label>
                      <div class="input-with-unit">
                        <el-input-number
                            :model-value="currentDateProperties.fontSize"
                            @change="updateDateComponentProperty('fontSize', $event)"
                            :min="8"
                            :max="72"
                            :step="1"
                            size="small"
                            :controls="false"
                            class="property-input-numeric"
                        />
                        <span class="unit-text">px</span>
                      </div>
                    </div>
                  </div>

                  <!-- 第2行：行间距 -->
                  <div class="property-row">
                    <div class="property-item-horizontal">
                      <label class="property-label-left">行间距:</label>
                      <div class="input-with-unit">
                        <el-input-number
                            :model-value="currentDateProperties.lineHeight"
                            @change="updateDateComponentProperty('lineHeight', $event)"
                            :min="0.5"
                            :max="5"
                            :step="0.1"
                            :precision="1"
                            size="small"
                            :controls="false"
                            class="property-input-numeric"
                        />
                        <span class="unit-text">px</span>
                      </div>
                    </div>
                  </div>

                  <!-- 第3行：样式按钮组 -->
                  <div class="property-row">
                    <div class="property-item-full">
                      <label class="property-label">样式</label>
                      <div class="font-style-buttons">
                        <button
                            type="button"
                            class="style-btn"
                            :class="{ active: currentDateProperties.fontWeight === 'bold' }"
                            @click="toggleDateFontWeight($event)"
                            title="粗体"
                        >
                          <strong>A</strong>
                        </button>
                        <button
                            type="button"
                            class="style-btn"
                            :class="{ active: currentDateProperties.fontStyle === 'italic' }"
                            @click="toggleDateFontStyle($event)"
                            title="斜体"
                        >
                          <em>I</em>
                        </button>
                        <button
                            type="button"
                            class="style-btn"
                            :class="{ active: currentDateProperties.textDecoration === 'underline' }"
                            @click="toggleDateTextDecoration($event)"
                            title="下划线"
                        >
                          <u>U</u>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 对齐方式 -->
                <div class="property-item-full">
                  <label class="property-label">对齐</label>
                  <div class="align-buttons">
                    <button
                        type="button"
                        class="align-btn"
                        :class="{ active: currentDateProperties.textAlign === 'left' }"
                        @click="updateDateComponentProperty('textAlign', 'left', $event)"
                        title="左对齐"
                    >
                      ≡
                    </button>
                    <button
                        type="button"
                        class="align-btn"
                        :class="{ active: currentDateProperties.textAlign === 'center' }"
                        @click="updateDateComponentProperty('textAlign', 'center', $event)"
                        title="居中对齐"
                    >
                      ≣
                    </button>
                    <button
                        type="button"
                        class="align-btn"
                        :class="{ active: currentDateProperties.textAlign === 'right' }"
                        @click="updateDateComponentProperty('textAlign', 'right', $event)"
                        title="右对齐"
                    >
                      ≡
                    </button>
                  </div>
                </div>

                <!-- 显示方式 -->
                <div class="property-item-full">
                  <label class="property-label">显示</label>
                  <el-select
                      :model-value="currentDateProperties.displayType"
                      @change="updateDateComponentProperty('displayType', $event)"
                      placeholder="选择显示方式"
                      size="small"
                      class="property-input-full"
                  >
                    <el-option label="中文繁体" value="traditional" />
                    <el-option label="中文简体" value="simplified" />
                    <el-option label="阿拉伯数字" value="arabic" />
                  </el-select>
                </div>

                <!-- 日期格式 -->
                <div class="property-item-full">
                  <label class="property-label">格式</label>
                  <el-select
                      :model-value="currentDateProperties.dateFormat"
                      @change="updateDateComponentProperty('dateFormat', $event)"
                      filterable
                      allow-create
                      placeholder="选择或输入日期格式"
                      size="small"
                      class="property-input-full"
                  >
                    <el-option label="YYYY-MM-DD" value="YYYY-MM-DD" />
                    <el-option label="YYYY/MM/DD" value="YYYY/MM/DD" />
                    <el-option label="DD/MM/YYYY" value="DD/MM/YYYY" />
                    <el-option label="MM/DD/YYYY" value="MM/DD/YYYY" />
                    <el-option label="YYYY年MM月DD日" value="YYYY年MM月DD日" />
                  </el-select>
                </div>

              </div>
            </template>

            <!-- 二维码组件属性 -->
            <template v-if="selectedComponent.type === 'qrcode'">
              <div class="property-group">
                <div class="property-grid">
                  <!-- ID属性 -->
                  <div class="property-item-full">
                    <label class="property-label">ID</label>
                    <el-input
                      :model-value="selectedComponent.properties.dataProperties?.qrcodeId || selectedComponent.properties.qrcodeId || ''"
                      @input="updateComponentDataProperty('qrcodeId', $event)"
                      placeholder="请输入二维码组件的唯一ID"
                      maxlength="50"
                      show-word-limit
                      size="small"
                      clearable
                      class="property-input-full"
                    />
                  </div>

                  <!-- 类型属性 -->
                  <div class="property-item-full">
                    <label class="property-label">类型</label>
                    <el-select
                      :model-value="selectedComponent.properties.dataProperties?.qrcodeType || selectedComponent.properties.qrcodeType"
                      @change="updateComponentDataProperty('qrcodeType', $event)"
                      placeholder="请选择"
                      size="small"
                      class="property-select-full"
                      popper-class="qrcode-select-dropdown"
                    >
                      <el-option
                        label="快速响应矩阵码（QR码）"
                        value="qr"
                      />
                      <el-option
                        label="网格矩阵码（GM码）"
                        value="gm"
                      />
                    </el-select>
                  </div>
                </div>
              </div>
            </template>

            <!-- 印章组件属性 -->
            <template v-if="selectedComponent.type === 'seal'">
              <el-form-item
                label="印章文字"
                prop="sealText"
                :rules="[{ required: true, message: '印章文字不能为空', trigger: 'blur' }]"
              >
                <el-input
                  :model-value="selectedComponent.properties.dataProperties?.sealText || selectedComponent.properties.sealText"
                  @input="updateComponentDataProperty('sealText', $event)"
                  placeholder="请输入印章文字（仅数据记录，建议4-8个字符）"
                  maxlength="8"
                  show-word-limit
                  size="small"
                  clearable
                />
              </el-form-item>

              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="印章颜色" prop="sealColor">
                    <el-color-picker
                      :model-value="selectedComponent.properties.dataProperties?.sealColor || selectedComponent.properties.sealColor"
                      @change="updateComponentDataProperty('sealColor', $event)"
                      show-alpha
                      :predefine="predefineColors"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="显示五角星" prop="showStar">
                    <el-switch
                      :model-value="selectedComponent.properties.dataProperties?.showStar || selectedComponent.properties.showStar"
                      @change="updateComponentDataProperty('showStar', $event)"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <!-- 复选框组件属性 -->
            <template v-if="selectedComponent.type === 'checkbox'">
              <el-form-item
                label="选项标签"
                prop="label"
                :rules="[{ required: true, message: '选项标签不能为空', trigger: 'blur' }]"
              >
                <el-input
                  :model-value="selectedComponent.properties.dataProperties?.label || selectedComponent.properties.label"
                  @input="updateComponentDataProperty('label', $event)"
                  placeholder="请输入复选框标签文字（仅数据记录）"
                  maxlength="50"
                  show-word-limit
                  size="small"
                  clearable
                />
              </el-form-item>

              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="默认选中" prop="checked">
                    <el-switch
                      :model-value="selectedComponent.properties.dataProperties?.checked || selectedComponent.properties.checked"
                      @change="updateComponentDataProperty('checked', $event)"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="复选框大小"
                    prop="checkboxSize"
                    :rules="[{ required: true, message: '复选框大小不能为空', trigger: 'blur' }]"
                  >
                    <el-input-number
                      :model-value="selectedComponent.properties.dataProperties?.checkboxSize || selectedComponent.properties.checkboxSize"
                      @change="updateComponentDataProperty('checkboxSize', $event)"
                      :min="12"
                      :max="32"
                      :step="1"
                      size="small"
                      controls-position="right"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item
                    label="字体大小"
                    prop="fontSize"
                    :rules="[{ required: true, message: '字体大小不能为空', trigger: 'blur' }]"
                  >
                    <el-input-number
                      :model-value="selectedComponent.properties.dataProperties?.fontSize || selectedComponent.properties.fontSize"
                      @change="updateComponentDataProperty('fontSize', $event)"
                      :min="8"
                      :max="32"
                      :step="1"
                      size="small"
                      controls-position="right"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="文字颜色" prop="textColor">
                    <el-color-picker
                      :model-value="selectedComponent.properties.dataProperties?.textColor || selectedComponent.properties.textColor"
                      @change="updateComponentDataProperty('textColor', $event)"
                      show-alpha
                      :predefine="predefineColors"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="边框颜色" prop="borderColor">
                    <el-color-picker
                      :model-value="selectedComponent.properties.dataProperties?.borderColor || selectedComponent.properties.borderColor"
                      @change="updateComponentDataProperty('borderColor', $event)"
                      show-alpha
                      :predefine="predefineColors"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="选中颜色" prop="checkedColor">
                    <el-color-picker
                      :model-value="selectedComponent.properties.dataProperties?.checkedColor || selectedComponent.properties.checkedColor"
                      @change="updateComponentDataProperty('checkedColor', $event)"
                      show-alpha
                      :predefine="predefineColors"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <!-- 单选框组件属性 -->
            <template v-if="selectedComponent.type === 'radio'">
              <el-form-item
                label="选项标签"
                prop="label"
                :rules="[{ required: true, message: '选项标签不能为空', trigger: 'blur' }]"
              >
                <el-input
                  :model-value="selectedComponent.properties.label"
                  @input="updateComponentProperty('label', $event)"
                  placeholder="请输入单选框标签文字"
                  maxlength="50"
                  show-word-limit
                  size="small"
                  clearable
                />
              </el-form-item>

              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="默认选中" prop="selected">
                    <el-switch
                      :model-value="selectedComponent.properties.selected"
                      @change="updateComponentProperty('selected', $event)"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="单选框大小"
                    prop="radioSize"
                    :rules="[{ required: true, message: '单选框大小不能为空', trigger: 'blur' }]"
                  >
                    <el-input-number
                      :model-value="selectedComponent.properties.radioSize"
                      @change="updateComponentProperty('radioSize', $event)"
                      :min="12"
                      :max="32"
                      :step="1"
                      size="small"
                      controls-position="right"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item
                    label="字体大小"
                    prop="fontSize"
                    :rules="[{ required: true, message: '字体大小不能为空', trigger: 'blur' }]"
                  >
                    <el-input-number
                      :model-value="selectedComponent.properties.fontSize"
                      @change="updateComponentProperty('fontSize', $event)"
                      :min="8"
                      :max="32"
                      :step="1"
                      size="small"
                      controls-position="right"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="文字颜色" prop="textColor">
                    <el-color-picker
                      :model-value="selectedComponent.properties.textColor"
                      @change="updateComponentProperty('textColor', $event)"
                      show-alpha
                      :predefine="predefineColors"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="边框颜色" prop="borderColor">
                    <el-color-picker
                      :model-value="selectedComponent.properties.borderColor"
                      @change="updateComponentProperty('borderColor', $event)"
                      show-alpha
                      :predefine="predefineColors"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="选中颜色" prop="selectedColor">
                    <el-color-picker
                      :model-value="selectedComponent.properties.selectedColor"
                      @change="updateComponentProperty('selectedColor', $event)"
                      show-alpha
                      :predefine="predefineColors"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import {
  QuestionFilled
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  selectedComponent: Object
})

// Emits
const emit = defineEmits(['property-change', 'component-delete'])

// 表单数据和状态
const formData = ref({})
const componentFormData = ref({})
const formLoading = ref(false)

// 文本内容的响应式变量
const textContent = ref('')

// 文本组件属性状态存储 - 按组件ID存储每个文本组件的属性配置
const textComponentStates = ref(new Map())

// 当前文本组件的属性状态
const currentTextProperties = ref({
  fontFamily: 'Microsoft YaHei',
  fontSize: 14,
  fontScale: 1,
  lineHeight: 1.4,
  letterSpacing: 0,
  fontWeight: 'normal',
  fontStyle: 'normal',
  textDecoration: 'none',
  textAlign: 'left',
  verticalAlign: 'middle',
  whiteSpace: 'normal',
  wordBreak: false,
  upperCase: false,
  color: '#000000'
})

// 日期组件属性状态存储 - 按组件ID存储每个日期组件的属性配置
const dateComponentStates = ref(new Map())

// 当前日期组件的属性状态
const currentDateProperties = ref({
  fontSize: 14,
  fontFamily: 'Microsoft YaHei',
  textAlign: 'left',
  dateFormat: 'YYYY-MM-DD',
  displayType: 'arabic',
  lineHeight: 1.4,
  fontWeight: 'normal',
  fontStyle: 'normal',
  textDecoration: 'none'
})



// 监听选中组件变化，更新表单数据
watch(() => props.selectedComponent, (newComponent) => {
  if (newComponent) {
    // 更新基础表单数据
    formData.value = {
      x: newComponent.x,
      y: newComponent.y,
      width: newComponent.width,
      height: newComponent.height
    }

    // 更新组件属性表单数据
    componentFormData.value = { ...newComponent.properties }

    // 更新文本内容
    if (newComponent.properties?.content !== undefined) {
      textContent.value = newComponent.properties.content
    }

    // 如果是文本组件，恢复其属性状态
    if (newComponent.type === 'text') {
      restoreTextComponentState(newComponent.id)
    }

    // 如果是日期组件，恢复其属性状态
    if (newComponent.type === 'date') {
      restoreDateComponentState(newComponent.id)
    }


  }
}, { immediate: true, deep: true })

// 恢复文本组件的属性状态
const restoreTextComponentState = (componentId) => {
  if (textComponentStates.value.has(componentId)) {
    // 恢复已保存的状态
    currentTextProperties.value = { ...textComponentStates.value.get(componentId) }
  } else {
    // 首次选中该组件，从组件的实际属性初始化状态
    const component = props.selectedComponent
    if (component && component.properties) {
      const initialState = {
        fontFamily: component.properties.fontFamily || 'Microsoft YaHei',
        fontSize: component.properties.fontSize || 14,
        fontScale: component.properties.fontScale || 1,
        lineHeight: component.properties.lineHeight || 1.4,
        letterSpacing: component.properties.letterSpacing || 0,
        fontWeight: component.properties.fontWeight || 'normal',
        fontStyle: component.properties.fontStyle || 'normal',
        textDecoration: component.properties.textDecoration || 'none',
        textAlign: component.properties.textAlign || 'left',
        verticalAlign: component.properties.verticalAlign || 'middle',
        whiteSpace: component.properties.whiteSpace || 'normal',
        wordBreak: component.properties.wordBreak || false,
        upperCase: component.properties.upperCase || false,
        color: component.properties.color || '#000000'
      }
      currentTextProperties.value = { ...initialState }
      textComponentStates.value.set(componentId, initialState)
    } else {
      // 如果组件属性不存在，使用默认状态
      const defaultState = {
        fontFamily: 'Microsoft YaHei',
        fontSize: 14,
        fontScale: 1,
        lineHeight: 1.4,
        letterSpacing: 0,
        fontWeight: 'normal',
        fontStyle: 'normal',
        textDecoration: 'none',
        textAlign: 'left',
        verticalAlign: 'middle',
        whiteSpace: 'normal',
        wordBreak: false,
        upperCase: false,
        color: '#000000'
      }
      currentTextProperties.value = { ...defaultState }
      textComponentStates.value.set(componentId, defaultState)
    }
  }
}

// 恢复日期组件的属性状态
const restoreDateComponentState = (componentId) => {
  if (dateComponentStates.value.has(componentId)) {
    // 恢复已保存的状态
    currentDateProperties.value = { ...dateComponentStates.value.get(componentId) }
  } else {
    // 首次选中该组件，从组件的实际属性初始化状态
    const component = props.selectedComponent
    if (component && component.properties) {
      const initialState = {
        fontSize: component.properties.fontSize || 14,
        fontFamily: component.properties.fontFamily || 'Microsoft YaHei',
        textAlign: component.properties.textAlign || 'left',
        dateFormat: component.properties.dateFormat || 'YYYY-MM-DD',
        displayType: component.properties.displayType || 'arabic',
        lineHeight: component.properties.lineHeight || 1.4,
        fontWeight: component.properties.fontWeight || 'normal',
        fontStyle: component.properties.fontStyle || 'normal',
        textDecoration: component.properties.textDecoration || 'none'
      }
      currentDateProperties.value = { ...initialState }
      dateComponentStates.value.set(componentId, initialState)
    } else {
      // 如果组件属性不存在，使用默认状态
      const defaultState = {
        fontSize: 14,
        fontFamily: 'Microsoft YaHei',
        textAlign: 'left',
        dateFormat: 'YYYY-MM-DD',
        displayType: 'arabic',
        lineHeight: 1.4,
        fontWeight: 'normal',
        fontStyle: 'normal',
        textDecoration: 'none'
      }
      currentDateProperties.value = { ...defaultState }
      dateComponentStates.value.set(componentId, defaultState)
    }
  }
}

// 保存文本组件的属性状态
const saveTextComponentState = (componentId, properties) => {
  textComponentStates.value.set(componentId, { ...properties })
}

// 保存日期组件的属性状态
const saveDateComponentState = (componentId, properties) => {
  dateComponentStates.value.set(componentId, { ...properties })
}



// 处理文本内容变化
const handleTextContentChange = (value) => {
  textContent.value = value
  updateComponentProperty('content', value)
}

// 预定义颜色
const predefineColors = ref([
  '#4381E6',  // 主题色
  '#3266BC',  // 主题色深色
  '#5A91EA',  // 主题色浅色
  '#FF4757',  // 红色
  '#FF6B35',  // 橙色
  '#FFA502',  // 黄色
  '#2ED573',  // 绿色
  '#1E90FF',  // 蓝色
  '#A55EEA',  // 紫色
  '#26D0CE',  // 青色
  '#000000',  // 黑色
  '#FFFFFF',  // 白色
  '#6C757D',  // 灰色
  '#DEE2E6',  // 浅灰色
])

// 字体选项
const fontOptions = ref([
  { value: 'Microsoft YaHei', label: '微软雅黑' },
  { value: 'SimSun', label: '宋体' },
  { value: 'SimHei', label: '黑体' },
  { value: 'Arial', label: 'Arial' },
  { value: 'Times New Roman', label: 'Times New Roman' },
  { value: 'Helvetica', label: 'Helvetica' },
  { value: 'Georgia', label: 'Georgia' },
  { value: 'Verdana', label: 'Verdana' }
])

// 形状选项
const shapeOptions = ref([
  { value: 'rectangle', label: '矩形', icon: '▭' },
  { value: 'circle', label: '圆形', icon: '●' },
  { value: 'triangle', label: '三角形', icon: '▲' },
  { value: 'diamond', label: '菱形', icon: '◆' },
  { value: 'star', label: '星形', icon: '★' },
  { value: 'hexagon', label: '六边形', icon: '⬡' }
])

// 更新基础属性（位置、尺寸等）
const updateProperty = (property, value) => {
  emit('property-change', property, value)
}

// 更新组件特定属性（影响画布渲染）
const updateComponentProperty = (property, value) => {
  if (props.selectedComponent) {
    const newProperties = { ...props.selectedComponent.properties }
    newProperties[property] = value
    emit('property-change', 'properties', newProperties)
  }
}

// 更新文本组件属性状态（仅存储状态，不影响画布渲染）
const updateTextComponentProperty = (property, value, event = null) => {
  // 🔧 防止表单提交和事件冒泡
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  console.log('🚀 updateTextComponentProperty called:', { property, value })

  if (props.selectedComponent && props.selectedComponent.type === 'text') {
    console.log('📝 Before update:', {
      componentId: props.selectedComponent.id,
      currentTextProperties: { ...currentTextProperties.value },
      note: '仅更新状态存储，不影响画布显示'
    })

    // 更新当前属性状态
    currentTextProperties.value[property] = value

    // 保存到状态存储中
    saveTextComponentState(props.selectedComponent.id, currentTextProperties.value)

    // 🔧 对齐属性完全解耦：仅存储状态，不更新组件数据
    if (property === 'textAlign' || property === 'verticalAlign') {
      console.log('📊 对齐属性仅存储状态，不影响画布显示')
    } else {
      // 对于非对齐属性，仍然可以选择是否更新组件数据
      // 根据需求决定是否启用以下代码
      console.log('🔄 非对齐属性，仅存储状态')
    }

    console.log('✅ After update:', {
      currentTextProperties: { ...currentTextProperties.value },
      note: '状态已更新，画布显示不受影响'
    })
  } else {
    console.log('❌ updateTextComponentProperty: Invalid component or type')
  }
}

// 更新日期组件属性状态（仅存储状态，不影响画布渲染）
const updateDateComponentProperty = (property, value, event = null) => {
  // 🔧 防止表单提交和事件冒泡
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  console.log('🚀 updateDateComponentProperty called:', { property, value })

  if (props.selectedComponent && props.selectedComponent.type === 'date') {
    console.log('📅 Before update:', {
      componentId: props.selectedComponent.id,
      currentDateProperties: { ...currentDateProperties.value },
      note: '仅更新状态存储，不影响画布显示'
    })

    // 更新当前属性状态
    currentDateProperties.value[property] = value

    // 保存到状态存储中
    saveDateComponentState(props.selectedComponent.id, currentDateProperties.value)

    // 🔧 日期组件属性完全解耦：仅存储状态，不更新组件数据
    console.log('📊 日期组件属性仅存储状态，不影响画布显示')

    console.log('✅ After update:', {
      currentDateProperties: { ...currentDateProperties.value },
      note: '状态已更新，画布显示不受影响'
    })
  } else {
    console.log('❌ updateDateComponentProperty: Invalid component or type')
  }
}

// 切换日期组件字体粗细
const toggleDateFontWeight = (event) => {
  event.preventDefault()
  event.stopPropagation()

  const newWeight = currentDateProperties.value.fontWeight === 'bold' ? 'normal' : 'bold'
  updateDateComponentProperty('fontWeight', newWeight, event)
}

// 切换日期组件字体样式（斜体）
const toggleDateFontStyle = (event) => {
  event.preventDefault()
  event.stopPropagation()

  const newStyle = currentDateProperties.value.fontStyle === 'italic' ? 'normal' : 'italic'
  updateDateComponentProperty('fontStyle', newStyle, event)
}

// 切换日期组件文本装饰（下划线）
const toggleDateTextDecoration = (event) => {
  event.preventDefault()
  event.stopPropagation()

  const newDecoration = currentDateProperties.value.textDecoration === 'underline' ? 'none' : 'underline'
  updateDateComponentProperty('textDecoration', newDecoration, event)
}

// 更新组件数据属性（仅数据记录，不影响画布渲染）
const updateComponentDataProperty = (property, value) => {
  if (props.selectedComponent) {
    // 将数据存储在一个专门的数据对象中，不影响画布渲染
    const newProperties = { ...props.selectedComponent.properties }
    if (!newProperties.dataProperties) {
      newProperties.dataProperties = {}
    }
    newProperties.dataProperties[property] = value
    emit('property-change', 'properties', newProperties)
  }
}

// 获取组件类型中文名称
const getComponentTypeName = (type) => {
  const typeNames = {
    text: '文本组件',
    date: '日期组件',
    image: '图片组件',
    qrcode: '二维码组件',
    table: '表格组件',
    seal: '印章组件',
    checkbox: '复选框组件',
    radio: '单选框组件'
  }
  return typeNames[type] || type
}

// 移除组件
const removeComponent = () => {
  if (props.selectedComponent) {
    emit('component-delete', props.selectedComponent.id)
  }
}

// 字体样式切换方法（仅更新状态存储）
const toggleFontWeight = (event = null) => {
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }
  const currentWeight = currentTextProperties.value.fontWeight
  updateTextComponentProperty('fontWeight', currentWeight === 'bold' ? 'normal' : 'bold', event)
}

const toggleFontStyle = (event = null) => {
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }
  const currentStyle = currentTextProperties.value.fontStyle
  updateTextComponentProperty('fontStyle', currentStyle === 'italic' ? 'normal' : 'italic', event)
}

const toggleTextDecoration = (event = null) => {
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }
  const currentDecoration = currentTextProperties.value.textDecoration
  updateTextComponentProperty('textDecoration', currentDecoration === 'underline' ? 'none' : 'underline', event)
}




</script>

<style scoped>
.property-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background-color: #f8f9fa;
}

/* 顶部标签页 */
.panel-tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
}

.tab-item {
  flex: 1;
  padding: 12px 16px;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  color: #6c757d;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-item.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background-color: #f8f9fa;
}

.tab-item:hover {
  color: #007bff;
  background-color: #f8f9fa;
}

/* 简化的组件标题 */
.component-header-simple {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px 6px 16px;
  border-bottom: 1px solid #e9ecef;
}

.component-title-simple {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin: 0;
}

.remove-btn-simple {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  font-size: 13px;
  padding: 2px 6px;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.remove-btn-simple:hover {
  background-color: #e3f2fd;
  color: #0056b3;
}

/* 属性分组 */
.property-group {
  padding: 6px 16px 16px 16px;
  border-bottom: 1px solid #e9ecef;
}

/* 日期组件属性分组 - 标签与"组件属性"标题的"组"字对齐，同时保持适当间距 */
/* 为整个属性分组添加统一的左内边距，让标题和标签都有适当的间距 */
.property-panel .property-group {
  padding-left: 10px !important;
}

/* 确保标题也有相同的左内边距，保持对齐 */
.property-group .group-title {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

/* 重置所有容器的额外边距，让标签与标题对齐 */
.property-group .property-grid {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

.property-group .property-grid .property-row {
  padding-left: 0 !important;
  margin-left: 0 !important;
}

.property-group .property-grid .property-item-horizontal {
  padding-left: 0 !important;
  margin-left: 0 !important;
}

.property-group .property-grid .property-item-full {
  padding-left: 0 !important;
  margin-left: 0 !important;
}

/* 确保所有标签都没有额外的左边距，与标题保持对齐 */
.property-group .property-label-left {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

.property-group .property-label {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

/* 为控件添加适当的左边距，保持标签与控件之间的视觉分离 */
.property-group .property-grid .el-select,
.property-group .property-grid .el-input-number,
.property-group .property-grid .el-color-picker,
.property-group .property-grid .font-style-buttons,
.property-group .property-grid .align-buttons {
  margin-left: 8px !important;
}

.property-group:last-child {
  border-bottom: none;
}

.group-title {
  font-size: 13px;
  font-weight: 500;
  color: #495057;
  margin: 0 0 5px 0;
  padding: 0;
}

/* 属性网格布局 */
.property-grid {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin: 0 0 5px 0;
}

.property-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5px;
}

.property-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: relative;
}

.property-item-horizontal {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  min-height: 30px;
}

.property-item-full {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 5px;
}

.property-item-checkbox {
  margin-bottom: 8px;
}

.property-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  margin: 0;
}

.property-label-left {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  margin: 0;
  min-width: 45px;
  text-align: left;
  flex-shrink: 0;
}

.property-label-with-icon {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.help-icon {
  font-size: 12px;
  color: #adb5bd;
  cursor: help;
}

/* 输入框和单位组合 */
.input-with-unit {
  display: flex;
  align-items: center;
  gap: 2px;
}

.property-input-numeric {
  width: 65px !important;
  flex-shrink: 0;
}

.property-input {
  width: 100% !important;
}

.property-input-full {
  width: 100%;
}

.property-select-compact {
  width: 100px;
  flex-shrink: 0;
}

.property-textarea {
  width: 100%;
  resize: vertical;
}

.unit-text {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  min-width: 20px;
  text-align: left;
  flex-shrink: 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-heading);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
}

.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: var(--color-text);
}

.no-selection-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-selection p {
  margin: 0;
  font-size: 14px;
  opacity: 0.7;
}

.property-sections {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

/* 移除原有的装饰性样式，改为简洁样式 */

.section-title {
  margin: 0;
  padding: 16px 20px;
  font-size: 15px;
  font-weight: 600;
  color: var(--color-heading);
  background: linear-gradient(135deg, var(--theme-primary), var(--theme-primary-hover));
  color: var(--theme-text-white);
  border: none;
}

/* Element Plus表单样式优化 - 统一间距 */
.property-group :deep(.el-form) {
  padding: 0;
}

.property-group :deep(.el-form-item) {
  margin-bottom: 10px;
}

.property-group :deep(.el-form-item__label) {
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
  line-height: 1.4;
  margin-bottom: 5px;
}

.property-group :deep(.el-form-item__content) {
  line-height: 1.4;
}

.property-group :deep(.el-form-item__error) {
  font-size: 12px;
  color: #f56c6c;
  padding-top: 4px;
}

/* 响应式栅格间距优化 */
.property-group :deep(.el-row) {
  margin-left: -6px;
  margin-right: -6px;
}

.property-group :deep(.el-col) {
  padding-left: 6px;
  padding-right: 6px;
}

/* Element Plus输入框样式 */
.property-item :deep(.el-input) {
  width: 100%;
}

.property-item :deep(.el-input__wrapper) {
  background-color: var(--color-background);
  border-color: var(--color-border);
  box-shadow: 0 0 0 1px var(--color-border);
}

.property-item :deep(.el-input__wrapper.is-focus) {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 1px var(--theme-primary);
}

.property-item :deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: var(--color-background-mute);
  cursor: not-allowed;
}

/* 字体样式按钮 */
.font-style-buttons {
  display: flex;
  gap: 2px;
  margin-left: 0px;
}

.style-btn {
  width: 24px;
  height: 24px;
  border: 1px solid #dee2e6;
  background-color: #fff;
  border-radius: 3px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #495057;
  transition: all 0.2s ease;
}

.style-btn:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.style-btn.active {
  background-color: #007bff;
  border-color: #007bff;
  color: #fff;
}

/* 对齐按钮 */
.align-buttons {
  display: flex;
  gap: 2px;
  flex-wrap: wrap;
}

.align-btn {
  width: 28px;
  height: 28px;
  border: 1px solid #dee2e6;
  background-color: #fff;
  border-radius: 3px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #495057;
  transition: all 0.2s ease;
}

.align-btn:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.align-btn.active {
  background-color: #007bff;
  border-color: #007bff;
  color: #fff;
}

/* Element Plus 组件样式覆盖 */
.property-panel :deep(.el-input-number) {
  width: 100%;
}

.property-panel :deep(.el-input-number.property-input-numeric) {
  width: 65px;
}

.property-panel :deep(.el-input-number .el-input__wrapper) {
  padding-right: 8px;
}

.property-panel :deep(.el-input__wrapper) {
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 12px;
  height: 28px;
}

.property-panel :deep(.el-input__wrapper.is-focus) {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.property-panel :deep(.el-select .el-input__wrapper) {
  height: 28px;
}

.property-panel :deep(.el-select.property-select-compact) {
  width: 100px;
}

.property-panel :deep(.el-select.property-select-compact .el-input__wrapper) {
  height: 28px;
}

.property-panel :deep(.el-textarea__inner) {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 12px;
  padding: 8px;
}

.property-panel :deep(.el-textarea__inner:focus) {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.property-panel :deep(.el-checkbox__label) {
  font-size: 12px;
  color: #495057;
}

/* 单位标签样式 */
.unit-label {
  font-size: 12px;
  color: var(--color-text);
  margin-left: 4px;
  opacity: 0.7;
}

.property-item :deep(.el-textarea__inner) {
  background-color: var(--color-background);
  border-color: var(--color-border);
  font-family: inherit;
}

.property-item :deep(.el-textarea__inner:focus) {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 1px var(--theme-primary);
}

/* Element Plus下拉选择样式 */
.property-item :deep(.el-select) {
  width: 100%;
}

.property-item :deep(.el-select .el-input__wrapper) {
  background-color: var(--color-background);
  border-color: var(--color-border);
}

.property-item :deep(.el-select .el-input__wrapper.is-focus) {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 1px var(--theme-primary);
}

.property-item :deep(.el-select-dropdown__item.selected) {
  color: var(--theme-primary);
  font-weight: bold;
}

/* Element Plus字数统计样式 */
.property-section :deep(.el-input__count) {
  color: var(--color-text);
  background-color: transparent;
}

.property-section :deep(.el-input__count.is-exceed) {
  color: #f56c6c;
}

/* 表单加载状态 */
.property-section :deep(.el-form.is-disabled) {
  opacity: 0.6;
  pointer-events: none;
}

.property-section :deep(.el-form.is-disabled .el-form-item__label) {
  color: var(--theme-text-disabled);
}

/* 表单验证状态样式 */
.property-section :deep(.el-form-item.is-error .el-input__wrapper) {
  border-color: #f56c6c;
  box-shadow: 0 0 0 1px #f56c6c;
}

.property-section :deep(.el-form-item.is-error .el-textarea__inner) {
  border-color: #f56c6c;
  box-shadow: 0 0 0 1px #f56c6c;
}

.property-section :deep(.el-form-item.is-success .el-input__wrapper) {
  border-color: #67c23a;
  box-shadow: 0 0 0 1px #67c23a;
}

/* Element Plus颜色选择器样式 */
.property-item :deep(.el-color-picker) {
  width: 100%;
}

.property-item :deep(.el-color-picker__trigger) {
  width: 100%;
  height: 32px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
}

/* Element Plus数字输入框样式 */
.property-item :deep(.el-input-number) {
  width: 100%;
}

.property-item :deep(.el-input-number .el-input__inner) {
  text-align: left;
  padding-right: 50px;
}

.property-item :deep(.el-input-number__increase),
.property-item :deep(.el-input-number__decrease) {
  background-color: var(--color-background-soft);
  border-color: var(--color-border);
}

.property-item :deep(.el-input-number__increase):hover,
.property-item :deep(.el-input-number__decrease):hover {
  background-color: var(--theme-primary);
  color: var(--theme-text-white);
}

/* 样式按钮组 */
.style-button-group {
  display: flex;
  gap: 4px;
  margin-top: 4px;
}

.style-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid var(--color-border);
  background-color: var(--color-background);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: var(--color-text);
}

.style-btn:hover {
  border-color: var(--theme-primary);
  background-color: var(--color-background-soft);
}

.style-btn.active {
  border-color: var(--theme-primary);
  background-color: var(--theme-primary);
  color: var(--theme-text-white);
}

/* 对齐按钮组 */
.align-button-group {
  display: flex;
  gap: 4px;
  margin-top: 4px;
}

.align-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid var(--color-border);
  background-color: var(--color-background);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: var(--color-text);
}

.align-btn:hover {
  border-color: var(--theme-primary);
  background-color: var(--color-background-soft);
}

.align-btn.active {
  border-color: var(--theme-primary);
  background-color: var(--theme-primary);
  color: var(--theme-text-white);
}

/* 属性提示文本 */
.property-hint {
  font-size: 12px;
  color: #666666;
  margin-top: 4px;
  padding: 4px 8px;
  background-color: var(--color-background-soft);
  border-radius: 4px;
  border-left: 3px solid var(--theme-primary);
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: var(--color-background-soft);
}

.panel-content::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}

/* 二维码组件属性样式修复 */
.property-input-full {
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.property-select-full {
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 确保二维码组件输入框和下拉框精确对齐 */
.property-panel .property-item-full .property-input-full,
.property-panel .property-item-full .property-select-full {
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* 修复Element Plus组件在二维码属性中的对齐 */
.property-panel .property-item-full :deep(.el-input),
.property-panel .property-item-full :deep(.el-select) {
  margin: 0 !important;
  padding: 0 !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.property-panel .property-item-full :deep(.el-input__wrapper),
.property-panel .property-item-full :deep(.el-select .el-input__wrapper) {
  margin: 0 !important;
  padding-left: 12px !important;
  padding-right: 12px !important;
  box-sizing: border-box !important;
  width: 100% !important;
}

/* 修复二维码下拉框弹出位置 */
.qrcode-select-dropdown {
  z-index: 9999 !important;
}

/* 确保下拉框容器不会被裁剪 */
.property-panel .property-group {
  overflow: visible !important;
}

.property-panel .property-grid {
  overflow: visible !important;
}

.property-panel .property-item-full {
  overflow: visible !important;
}

/* 修复Element Plus下拉框在属性面板中的定位问题 */
.property-panel :deep(.el-select-dropdown) {
  z-index: 9999 !important;
  position: fixed !important;
}

/* 确保二维码组件属性标签与标题对齐 */
.property-panel .property-group .property-label {
  margin-left: 0 !important;
  padding-left: 0 !important;
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  margin-bottom: 5px;
}
</style>
