<template>
  <div class="checkbox-component" :style="componentStyle">
    <div class="checkbox-container" :style="containerStyle">
      <div class="checkbox-box" :style="checkboxStyle">
        <svg
          v-if="isChecked"
          width="12"
          height="12"
          viewBox="0 0 24 24"
          class="checkbox-check"
        >
          <path
            d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"
            fill="currentColor"
          />
        </svg>
      </div>
      <span class="checkbox-label" :style="labelStyle">
        {{ checkboxLabel }}
      </span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  componentData: {
    type: Object,
    required: true
  }
})

// 计算复选框状态
const isChecked = computed(() => {
  return props.componentData.properties.checked === true
})

// 计算复选框标签
const checkboxLabel = computed(() => {
  return props.componentData.properties.label || '复选框选项'
})

// 计算组件样式
const componentStyle = computed(() => {
  const properties = props.componentData.properties
  return {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    // 标准化基础属性
    transform: `rotate(${properties.rotation || 0}deg)`,
    opacity: (properties.opacity || 100) / 100
  }
})

// 计算容器样式
const containerStyle = computed(() => {
  return {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '4px 8px',
    boxSizing: 'border-box'
  }
})

// 计算复选框样式
const checkboxStyle = computed(() => {
  const properties = props.componentData.properties
  const size = properties.checkboxSize || 16
  
  return {
    width: size + 'px',
    height: size + 'px',
    border: `2px solid ${properties.borderColor || '#666'}`,
    borderRadius: '2px',
    backgroundColor: isChecked.value ? (properties.checkedColor || '#1976d2') : (properties.backgroundColor || '#fff'),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexShrink: 0,
    color: properties.checkColor || '#fff',
    cursor: 'pointer',
    transition: 'all 0.2s ease'
  }
})

// 计算标签样式
const labelStyle = computed(() => {
  const properties = props.componentData.properties
  return {
    fontSize: (properties.fontSize || 14) + 'px',
    color: properties.textColor || '#333',
    fontFamily: properties.fontFamily || 'Microsoft YaHei',
    lineHeight: '1.4',
    cursor: 'pointer',
    userSelect: 'none',
    flex: 1
  }
})
</script>

<style scoped>
.checkbox-component {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.checkbox-container {
  user-select: none;
}

.checkbox-box:hover {
  border-color: #1976d2;
}

.checkbox-check {
  display: block;
}

.checkbox-label {
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
