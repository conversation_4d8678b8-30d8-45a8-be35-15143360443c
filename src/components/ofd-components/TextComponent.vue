<template>
  <div
    class="text-component"
    :style="textStyle"
  >
    <div class="text-content" :style="contentStyle">
      {{ displayContent }}
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  componentData: {
    type: Object,
    required: true
  }
})

// 注意：对齐功能已解耦，不再影响画布显示
// 对齐设置仅用于数据存储，组件使用固定的默认对齐样式

// 计算文本样式 - 使用固定对齐样式，不受对齐按钮影响
const textStyle = computed(() => {
  const properties = props.componentData.properties

  console.log('🔍 TextComponent - textStyle computed (fixed alignment):', {
    componentId: props.componentData.id,
    note: '对齐样式已固定，不受properties影响'
  })

  const computedStyle = {
    width: '100%',
    height: '100%',
    display: 'flex',
    // 🔧 固定对齐样式 - 确保组件始终可见
    alignItems: 'flex-start',      // 固定顶部对齐
    justifyContent: 'flex-start',  // 固定左对齐
    padding: '4px',
    boxSizing: 'border-box',
    overflow: 'hidden',
    // 确保组件始终可见
    minWidth: '20px',
    minHeight: '20px',
    // 标准化基础属性
    transform: `rotate(${properties.rotation || 0}deg)`,
    opacity: Math.max(0.1, (properties.opacity || 100) / 100), // 确保最小透明度，避免完全透明
    // 确保容器可见性
    position: 'relative',
    // 临时调试边框
    border: '2px solid green',
    backgroundColor: 'rgba(0, 255, 0, 0.1)'
  }

  console.log('🎨 TextComponent - fixed style applied:', computedStyle)

  return computedStyle
})

// 计算文本内容样式
const contentStyle = computed(() => {
  const properties = props.componentData.properties

  return {
    fontSize: (properties.fontSize * (properties.fontScale || 1)) + 'px',
    color: properties.color || '#000000',
    fontFamily: properties.fontFamily || '宋体',
    fontWeight: properties.fontWeight || 'normal',
    fontStyle: properties.fontStyle || 'normal',
    textDecoration: properties.textDecoration || 'none',
    lineHeight: properties.lineHeight || 1.4,
    letterSpacing: (properties.letterSpacing || 0) + 'px',
    textAlign: properties.textAlign || 'left',
    whiteSpace: properties.whiteSpace || 'normal',
    wordBreak: properties.wordBreak ? 'break-all' : 'normal',
    // 确保文本内容可见
    width: '100%',
    minHeight: '1em'
  }
})

// 计算显示内容
const displayContent = computed(() => {
  const content = props.componentData.properties.content || ''
  const upperCase = props.componentData.properties.upperCase

  if (upperCase && /^\d+$/.test(content)) {
    // 将数字转换为大写中文数字
    const nums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
    return content.split('').map(digit => nums[parseInt(digit)]).join('')
  }

  return content
})

// 在 <script setup> 中，所有的响应式变量都会自动暴露给模板
</script>

<style scoped>
.text-component {
  user-select: none;
  line-height: 1.4;
  /* 🔧 固定对齐样式 - 确保组件始终可见且对齐一致 */
  justify-content: flex-start !important;  /* 固定左对齐 */
  align-items: flex-start !important;      /* 固定顶部对齐 */
}

/* 注意：已移除动态对齐样式，对齐按钮仅用于状态记录 */
</style>
