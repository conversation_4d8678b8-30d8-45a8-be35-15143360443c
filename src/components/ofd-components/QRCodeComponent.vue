<template>
  <div class="qrcode-component" :style="componentStyle">
    <div v-if="componentData.properties.content" class="qrcode-display" :style="qrcodeStyle">
      <!-- 这里使用简单的二维码占位符，实际项目中可以集成qrcode.js库 -->
      <div class="qrcode-placeholder">
        <div class="qrcode-pattern">
          <div class="qr-row" v-for="row in qrPattern" :key="row.id">
            <div
              v-for="cell in row.cells"
              :key="cell.id"
              class="qr-cell"
              :class="{ 'qr-filled': cell.filled }"
            ></div>
          </div>
        </div>
        <div class="qrcode-text">{{ truncatedContent }}</div>
      </div>
    </div>
    <div v-else class="qrcode-empty">
      <div class="empty-icon">📱</div>
      <div class="empty-text">请输入二维码内容</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  componentData: {
    type: Object,
    required: true
  }
})

// 生成简单的二维码图案（模拟）
const qrPattern = computed(() => {
  const size = 8 // 8x8的简化二维码
  const pattern = []
  
  for (let i = 0; i < size; i++) {
    const row = {
      id: i,
      cells: []
    }
    
    for (let j = 0; j < size; j++) {
      // 简单的伪随机模式，基于内容生成
      const content = props.componentData.properties.content || ''
      const seed = content.length + i * size + j
      const filled = (seed * 7 + i * 3 + j * 5) % 3 === 0
      
      row.cells.push({
        id: j,
        filled: filled
      })
    }
    
    pattern.push(row)
  }
  
  return pattern
})

// 截断显示的内容
const truncatedContent = computed(() => {
  const content = props.componentData.properties.content || ''
  return content.length > 20 ? content.substring(0, 20) + '...' : content
})

// 计算组件样式
const componentStyle = computed(() => {
  const properties = props.componentData.properties
  return {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    // 标准化基础属性
    transform: `rotate(${properties.rotation || 0}deg)`,
    opacity: (properties.opacity || 100) / 100
  }
})

// 计算二维码样式
const qrcodeStyle = computed(() => {
  const properties = props.componentData.properties
  return {
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '8px',
    boxSizing: 'border-box',
    backgroundColor: properties.backgroundColor || '#fff',
    border: `1px solid ${properties.borderColor || '#ddd'}`,
    borderRadius: '4px'
  }
})
</script>

<style scoped>
.qrcode-component {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.qrcode-display {
  user-select: none;
}

.qrcode-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.qrcode-pattern {
  display: grid;
  grid-template-rows: repeat(8, 1fr);
  gap: 1px;
  background-color: #000;
  padding: 4px;
  border-radius: 2px;
  max-width: 80px;
  max-height: 80px;
  aspect-ratio: 1;
}

.qr-row {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 1px;
}

.qr-cell {
  background-color: #fff;
  aspect-ratio: 1;
  border-radius: 1px;
}

.qr-cell.qr-filled {
  background-color: #000;
}

.qrcode-text {
  font-size: 10px;
  color: #666;
  text-align: center;
  word-break: break-all;
  max-width: 100px;
}

.qrcode-empty {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  border: 1px dashed #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.empty-icon {
  font-size: 24px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.empty-text {
  font-size: 12px;
  opacity: 0.7;
}
</style>
