<template>
  <div class="shape-component">
    <!-- 矩形 -->
    <div 
      v-if="componentData.properties.shapeType === 'rectangle'"
      class="shape-rectangle"
      :style="rectangleStyle"
    ></div>
    
    <!-- 圆形 -->
    <div 
      v-else-if="componentData.properties.shapeType === 'circle'"
      class="shape-circle"
      :style="circleStyle"
    ></div>
    
    <!-- 三角形 -->
    <div 
      v-else-if="componentData.properties.shapeType === 'triangle'"
      class="shape-triangle"
      :style="triangleStyle"
    ></div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  componentData: {
    type: Object,
    required: true
  }
})

// 矩形样式
const rectangleStyle = computed(() => {
  const properties = props.componentData.properties
  return {
    width: '100%',
    height: '100%',
    backgroundColor: properties.fillColor,
    border: `${properties.borderWidth}px solid ${properties.borderColor}`,
    boxSizing: 'border-box'
  }
})

// 圆形样式
const circleStyle = computed(() => {
  const properties = props.componentData.properties
  return {
    width: '100%',
    height: '100%',
    backgroundColor: properties.fillColor,
    border: `${properties.borderWidth}px solid ${properties.borderColor}`,
    borderRadius: '50%',
    boxSizing: 'border-box'
  }
})

// 三角形样式
const triangleStyle = computed(() => {
  const properties = props.componentData.properties
  const size = Math.min(props.componentData.width, props.componentData.height)
  
  return {
    width: 0,
    height: 0,
    borderLeft: `${size / 2}px solid transparent`,
    borderRight: `${size / 2}px solid transparent`,
    borderBottom: `${size}px solid ${properties.fillColor}`,
    margin: 'auto'
  }
})
</script>

<style scoped>
.shape-component {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shape-rectangle,
.shape-circle {
  user-select: none;
}

.shape-triangle {
  user-select: none;
}
</style>
