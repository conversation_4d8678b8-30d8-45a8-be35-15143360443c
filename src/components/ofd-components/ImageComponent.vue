<template>
  <div class="image-component" :style="componentStyle">
    <img
      v-if="componentData.properties.src"
      :src="componentData.properties.src"
      :alt="componentData.properties.alt"
      class="image-content"
      @error="handleImageError"
      @load="handleImageLoad"
    />
    <div v-else class="image-placeholder">
      <div class="placeholder-icon">🖼️</div>
      <div class="placeholder-text">点击添加图片</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  componentData: {
    type: Object,
    required: true
  }
})

// 响应式数据
const imageError = ref(false)

// 计算组件样式
const componentStyle = computed(() => {
  const properties = props.componentData.properties
  return {
    // 标准化基础属性
    transform: `rotate(${properties.rotation || 0}deg)`,
    opacity: (properties.opacity || 100) / 100
  }
})

// 图片加载错误处理
const handleImageError = () => {
  imageError.value = true
  console.error('图片加载失败:', props.componentData.properties.src)
}

// 图片加载成功处理
const handleImageLoad = () => {
  imageError.value = false
}
</script>

<style scoped>
.image-component {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #f8f9fa;
  border: 1px dashed #dee2e6;
}

.image-content {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  user-select: none;
  pointer-events: none;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  text-align: center;
  padding: 16px;
}

.placeholder-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.placeholder-text {
  font-size: 12px;
  opacity: 0.7;
}
</style>
