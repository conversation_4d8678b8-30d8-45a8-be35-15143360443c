<template>
  <div class="date-component" :style="componentStyle">
    <div class="date-display" :style="dateStyle">
      {{ formattedDate }}
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  componentData: {
    type: Object,
    required: true
  }
})

// 计算格式化的日期 - 动态显示当天日期，支持自定义格式和不同显示方式
const formattedDate = computed(() => {
  const today = new Date()
  const properties = props.componentData.properties
  const dateFormat = properties.dateFormat || 'YYYY-MM-DD'
  const displayType = properties.displayType || 'arabic'

  // 获取日期组件
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')

  // 根据显示方式转换数字
  const convertNumber = (num, type) => {
    const numStr = String(num).padStart(2, '0')

    switch (type) {
      case 'traditional': // 中文繁体
        const traditionalMap = {
          '0': '零', '1': '壹', '2': '贰', '3': '叁', '4': '肆',
          '5': '伍', '6': '陆', '7': '柒', '8': '捌', '9': '玖'
        }
        return numStr.split('').map(digit => traditionalMap[digit] || digit).join('')

      case 'simplified': // 中文简体
        const simplifiedMap = {
          '0': '零', '1': '一', '2': '二', '3': '三', '4': '四',
          '5': '五', '6': '六', '7': '七', '8': '八', '9': '九'
        }
        return numStr.split('').map(digit => simplifiedMap[digit] || digit).join('')

      case 'arabic': // 阿拉伯数字（默认）
      default:
        return numStr
    }
  }

  // 转换年月日
  const displayYear = displayType === 'arabic' ? year : convertNumber(year, displayType)
  const displayMonth = convertNumber(month, displayType)
  const displayDay = convertNumber(day, displayType)

  // 支持自定义格式的通用格式化函数
  const formatDate = (format) => {
    return format
      .replace(/YYYY/g, displayYear)
      .replace(/MM/g, displayMonth)
      .replace(/DD/g, displayDay)
      .replace(/年/g, '年')
      .replace(/月/g, '月')
      .replace(/日/g, '日')
  }

  // 预设格式的快速处理（保持向后兼容）
  switch (dateFormat) {
    case 'YYYY/MM/DD':
      return `${displayYear}/${displayMonth}/${displayDay}`
    case 'DD/MM/YYYY':
      return `${displayDay}/${displayMonth}/${displayYear}`
    case 'MM/DD/YYYY':
      return `${displayMonth}/${displayDay}/${displayYear}`
    case 'YYYY年MM月DD日':
      return `${displayYear}年${displayMonth}月${displayDay}日`
    case 'YYYY-MM-DD':
      return `${displayYear}-${displayMonth}-${displayDay}`
    default:
      // 对于自定义格式，使用通用格式化函数
      try {
        return formatDate(dateFormat)
      } catch (error) {
        console.warn('日期格式化错误，使用默认格式:', error)
        return `${displayYear}-${displayMonth}-${displayDay}`
      }
  }
})

// 计算组件样式
const componentStyle = computed(() => {
  const properties = props.componentData.properties
  return {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    // 标准化基础属性
    transform: `rotate(${properties.rotation || 0}deg)`,
    opacity: (properties.opacity || 100) / 100
  }
})

// 计算日期样式 - 画布中保持简洁的静态样式
// 注意：这里我们仍然使用静态样式，但为将来扩展做准备，可以从properties中获取值
const dateStyle = computed(() => {
  const properties = props.componentData.properties
  return {
    // 使用静态值，但为将来扩展做准备
    fontSize: '14px', // 将来可以使用 `${properties.fontSize}px`
    color: '#000000', // 将来可以使用 properties.color
    fontFamily: 'Microsoft YaHei', // 将来可以使用 properties.fontFamily
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center', // 将来可以根据 properties.textAlign 调整
    padding: '4px 8px',
    boxSizing: 'border-box',
    border: '1px solid #ddd',
    backgroundColor: '#fff',
    borderRadius: '4px',
    textAlign: 'center' // 将来可以使用 properties.textAlign
  }
})
</script>

<style scoped>
.date-component {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.date-display {
  user-select: none;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
