<template>
  <div class="radio-component" :style="componentStyle">
    <div class="radio-container" :style="containerStyle">
      <div class="radio-box" :style="radioStyle">
        <div
          v-if="isSelected"
          class="radio-dot"
          :style="dotStyle"
        ></div>
      </div>
      <span class="radio-label" :style="labelStyle">
        {{ radioLabel }}
      </span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  componentData: {
    type: Object,
    required: true
  }
})

// 计算单选框状态
const isSelected = computed(() => {
  return props.componentData.properties.selected === true
})

// 计算单选框标签
const radioLabel = computed(() => {
  return props.componentData.properties.label || '单选框选项'
})

// 计算组件样式
const componentStyle = computed(() => {
  const properties = props.componentData.properties
  return {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    // 标准化基础属性
    transform: `rotate(${properties.rotation || 0}deg)`,
    opacity: (properties.opacity || 100) / 100
  }
})

// 计算容器样式
const containerStyle = computed(() => {
  return {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '4px 8px',
    boxSizing: 'border-box'
  }
})

// 计算单选框样式
const radioStyle = computed(() => {
  const properties = props.componentData.properties
  const size = properties.radioSize || 16
  
  return {
    width: size + 'px',
    height: size + 'px',
    border: `2px solid ${properties.borderColor || '#666'}`,
    borderRadius: '50%',
    backgroundColor: properties.backgroundColor || '#fff',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexShrink: 0,
    cursor: 'pointer',
    transition: 'all 0.2s ease'
  }
})

// 计算选中点样式
const dotStyle = computed(() => {
  const properties = props.componentData.properties
  const size = (properties.radioSize || 16) * 0.5
  
  return {
    width: size + 'px',
    height: size + 'px',
    backgroundColor: properties.selectedColor || '#1976d2',
    borderRadius: '50%'
  }
})

// 计算标签样式
const labelStyle = computed(() => {
  const properties = props.componentData.properties
  return {
    fontSize: (properties.fontSize || 14) + 'px',
    color: properties.textColor || '#333',
    fontFamily: properties.fontFamily || 'Microsoft YaHei',
    lineHeight: '1.4',
    cursor: 'pointer',
    userSelect: 'none',
    flex: 1
  }
})
</script>

<style scoped>
.radio-component {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.radio-container {
  user-select: none;
}

.radio-box:hover {
  border-color: #1976d2;
}

.radio-dot {
  transition: all 0.2s ease;
}

.radio-label {
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
