<template>
  <div class="table-component" :style="componentStyle">
    <table class="ofd-table" :style="tableStyle">
      <tbody>
        <tr v-for="row in rows" :key="row">
          <td
            v-for="col in cols"
            :key="col"
            class="table-cell"
            :style="cellStyle"
          >
            单元格 {{ row }}-{{ col }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  componentData: {
    type: Object,
    required: true
  }
})

// 计算行数和列数 - 使用固定默认值
const rows = computed(() => {
  return Array.from({ length: 3 }, (_, i) => i + 1) // 固定3行
})

const cols = computed(() => {
  return Array.from({ length: 3 }, (_, i) => i + 1) // 固定3列
})

// 计算组件样式
const componentStyle = computed(() => {
  const properties = props.componentData.properties
  return {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    // 标准化基础属性
    transform: `rotate(${properties.rotation || 0}deg)`,
    opacity: (properties.opacity || 100) / 100
  }
})

// 表格样式 - 使用固定默认样式
const tableStyle = computed(() => {
  return {
    width: '100%',
    height: '100%',
    borderCollapse: 'collapse',
    border: '1px solid #000000' // 固定边框样式
  }
})

// 单元格样式 - 使用固定默认样式
const cellStyle = computed(() => {
  return {
    border: '1px solid #000000', // 固定边框样式
    padding: '4px 8px',
    fontSize: '12px',
    textAlign: 'center',
    verticalAlign: 'middle'
  }
})
</script>

<style scoped>
.table-component {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.ofd-table {
  user-select: none;
  font-family: Arial, sans-serif;
}

.table-cell {
  background-color: white;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-cell:hover {
  background-color: #f8f9fa;
}
</style>
