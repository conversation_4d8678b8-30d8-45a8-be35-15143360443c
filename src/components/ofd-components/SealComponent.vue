<template>
  <div class="seal-component" :style="componentStyle">
    <div class="seal-container" :style="sealStyle">
      <svg
        :width="sealSize"
        :height="sealSize"
        viewBox="0 0 100 100"
        class="seal-svg"
      >
        <!-- 外圆 -->
        <circle 
          cx="50" 
          cy="50" 
          r="48" 
          fill="none" 
          :stroke="sealColor" 
          :stroke-width="strokeWidth"
        />
        
        <!-- 内圆 -->
        <circle 
          cx="50" 
          cy="50" 
          r="35" 
          fill="none" 
          :stroke="sealColor" 
          :stroke-width="strokeWidth * 0.7"
        />
        
        <!-- 印章文字 -->
        <text 
          x="50" 
          y="35" 
          text-anchor="middle" 
          :fill="sealColor" 
          :font-size="fontSize"
          font-family="SimSun, serif"
          font-weight="bold"
        >
          {{ topText }}
        </text>
        
        <text 
          x="50" 
          y="55" 
          text-anchor="middle" 
          :fill="sealColor" 
          :font-size="fontSize"
          font-family="SimSun, serif"
          font-weight="bold"
        >
          {{ bottomText }}
        </text>
        
        <!-- 中心五角星 -->
        <polygon 
          points="50,20 52,26 58,26 53,30 55,36 50,32 45,36 47,30 42,26 48,26"
          :fill="sealColor"
          v-if="showStar"
        />
      </svg>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  componentData: {
    type: Object,
    required: true
  }
})

// 计算印章文字
const topText = computed(() => {
  const text = props.componentData.properties.sealText || '印章文字'
  return text.length > 4 ? text.substring(0, 4) : text
})

const bottomText = computed(() => {
  const text = props.componentData.properties.sealText || '印章文字'
  return text.length > 4 ? text.substring(4, 8) : ''
})

// 计算印章尺寸
const sealSize = computed(() => {
  const { width, height } = props.componentData
  return Math.min(width, height) - 4 // 留出边距
})

// 计算字体大小
const fontSize = computed(() => {
  const size = sealSize.value
  return Math.max(8, size * 0.12) // 根据印章大小调整字体
})

// 计算线条宽度
const strokeWidth = computed(() => {
  const size = sealSize.value
  return Math.max(1, size * 0.03) // 根据印章大小调整线条宽度
})

// 印章颜色
const sealColor = computed(() => {
  return props.componentData.properties.sealColor || '#d32f2f'
})

// 是否显示五角星
const showStar = computed(() => {
  return props.componentData.properties.showStar !== false
})

// 计算组件样式
const componentStyle = computed(() => {
  const properties = props.componentData.properties
  return {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    // 标准化基础属性
    transform: `rotate(${properties.rotation || 0}deg)`,
    opacity: (properties.opacity || 100) / 100
  }
})

// 计算印章样式
const sealStyle = computed(() => {
  return {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '2px',
    boxSizing: 'border-box'
  }
})
</script>

<style scoped>
.seal-component {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.seal-container {
  user-select: none;
}

.seal-svg {
  display: block;
  max-width: 100%;
  max-height: 100%;
}

/* 确保SVG文字清晰显示 */
.seal-svg text {
  text-rendering: optimizeLegibility;
  shape-rendering: crispEdges;
}
</style>
