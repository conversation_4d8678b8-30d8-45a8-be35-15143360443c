// 测试数据和工具函数

// 示例模板数据
export const sampleTemplate = {
  name: "示例OFD模板",
  components: [
    {
      id: "1",
      type: "text",
      x: 50,
      y: 50,
      width: 200,
      height: 40,
      properties: {
        content: "OFD模板设计器",
        fontSize: 18,
        color: "#333333",
        fontFamily: "Microsoft YaHei"
      }
    },
    {
      id: "2",
      type: "text",
      x: 50,
      y: 120,
      width: 300,
      height: 30,
      properties: {
        content: "这是一个示例文本组件",
        fontSize: 14,
        color: "#666666",
        fontFamily: "SimSun"
      }
    },
    {
      id: "3",
      type: "table",
      x: 400,
      y: 50,
      width: 200,
      height: 120,
      properties: {
        // 表格属性已简化，移除了 rows、cols、borderWidth、borderColor
      }
    }
  ]
}

// 组件默认配置
export const componentDefaults = {
  text: {
    width: 120,
    height: 30,
    properties: {
      content: "新文本",
      fontSize: 14,
      color: "#000000",
      fontFamily: "Microsoft YaHei",
      fontWeight: "normal",
      fontStyle: "normal",
      textDecoration: "none",
      fontScale: 1,
      lineHeight: 1.4,
      letterSpacing: 0,
      textAlign: "left",
      verticalAlign: "middle",
      whiteSpace: "normal",
      wordBreak: false,
      upperCase: false,
      hidden: false,
      // 标准化基础属性
      rotation: 0,
      opacity: 100,
      dataBinding: ""
    }
  },
  date: {
    width: 120,
    height: 30,
    properties: {
      // 日期格式
      dateFormat: "YYYY-MM-DD",
      displayType: "arabic",
      // 文字样式
      fontSize: 14,
      fontFamily: "Microsoft YaHei",
      fontWeight: "normal",
      fontStyle: "normal",
      textDecoration: "none",
      lineHeight: 1.4,
      letterSpacing: 0,
      textAlign: "left",
      fontAutoSize: false,
      wordBreak: false,
      // 外观
      borderColor: "#ddd",
      backgroundColor: "#fff",
      // 标准化基础属性
      rotation: 0,
      opacity: 100,
      dataBinding: ""
    }
  },
  image: {
    width: 100,
    height: 100,
    properties: {
      src: "",
      alt: "图片",
      // 标准化基础属性
      rotation: 0,
      opacity: 100,
      dataBinding: ""
    }
  },
  qrcode: {
    width: 80,
    height: 80,
    properties: {
      content: "https://example.com",
      backgroundColor: "#fff",
      borderColor: "#ddd",
      // 标准化基础属性
      rotation: 0,
      opacity: 100,
      dataBinding: ""
    }
  },
  table: {
    width: 200,
    height: 120,
    properties: {
      rows: 3,
      cols: 3,
      borderWidth: 1,
      borderColor: "#000000",
      // 标准化基础属性
      rotation: 0,
      opacity: 100,
      dataBinding: ""
    }
  },
  seal: {
    width: 80,
    height: 80,
    properties: {
      sealText: "印章文字",
      sealColor: "#d32f2f",
      showStar: true,
      // 标准化基础属性
      rotation: 0,
      opacity: 100,
      dataBinding: ""
    }
  },
  checkbox: {
    width: 120,
    height: 30,
    properties: {
      label: "复选框选项",
      checked: false,
      checkboxSize: 16,
      fontSize: 14,
      textColor: "#333",
      borderColor: "#666",
      backgroundColor: "#fff",
      checkedColor: "#1976d2",
      checkColor: "#fff",
      fontFamily: "Microsoft YaHei",
      // 标准化基础属性
      rotation: 0,
      opacity: 100,
      dataBinding: ""
    }
  },
  radio: {
    width: 120,
    height: 30,
    properties: {
      label: "单选框选项",
      selected: false,
      radioSize: 16,
      fontSize: 14,
      textColor: "#333",
      borderColor: "#666",
      backgroundColor: "#fff",
      selectedColor: "#1976d2",
      fontFamily: "Microsoft YaHei",
      // 标准化基础属性
      rotation: 0,
      opacity: 100,
      dataBinding: ""
    }
  }
}

// 工具函数
export const generateId = () => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9)
}

export const cloneComponent = (component) => {
  return JSON.parse(JSON.stringify(component))
}

export const validateComponent = (component) => {
  const required = ['id', 'type', 'x', 'y', 'width', 'height', 'properties']
  return required.every(prop => component.hasOwnProperty(prop))
}
