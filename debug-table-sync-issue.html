<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格数据同步问题调试指南</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
            color: white;
            border-radius: 8px;
        }
        .debug-section {
            background: #fff3cd;
            border: 2px solid #ffc107;
            color: #856404;
            padding: 25px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 6px solid #ffc107;
        }
        .test-steps {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }
        .step-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #FF6B6B;
            padding-bottom: 10px;
        }
        .step-list {
            list-style: none;
            padding: 0;
            counter-reset: step-counter;
        }
        .step-list li {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
            counter-increment: step-counter;
            position: relative;
            padding-left: 50px;
        }
        .step-list li:before {
            content: "步骤" counter(step-counter);
            position: absolute;
            left: 0;
            top: 15px;
            background: #FF6B6B;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 11px;
        }
        .expected-output {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }
        .scenario {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            color: #0d47a1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 表格数据同步问题调试</h1>
            <p>通过调试信息定位问题根源</p>
        </div>

        <div class="debug-section">
            <h3>🎯 调试信息已添加</h3>
            <p><strong>我已经在PropertyPanel.vue中添加了详细的调试信息显示。</strong></p>
            <p>现在当您选择任何组件时，都会在属性面板顶部看到一个灰色背景的调试信息框，显示当前选中组件的详细信息。</p>
        </div>

        <div class="test-steps">
            <div class="step-title">📋 调试测试步骤</div>
            
            <ol class="step-list">
                <li>
                    <strong>访问应用</strong><br>
                    打开 <a href="http://localhost:5173" target="_blank">http://localhost:5173</a>
                </li>
                <li>
                    <strong>查看画布</strong><br>
                    确认画布上是否有表格组件（应该在右侧位置，x=400, y=50）
                </li>
                <li>
                    <strong>点击表格组件</strong><br>
                    点击画布上的表格组件，观察是否有选中边框出现
                </li>
                <li>
                    <strong>查看调试信息</strong><br>
                    在右侧PropertyPanel的顶部应该出现灰色背景的调试信息框
                </li>
                <li>
                    <strong>分析调试输出</strong><br>
                    根据调试信息的内容，我们可以确定问题的具体位置
                </li>
            </ol>
        </div>

        <div class="test-steps">
            <div class="step-title">🔍 可能的调试结果分析</div>
            
            <div class="scenario">
                <h4>场景1：没有调试信息显示</h4>
                <p><strong>可能原因：</strong></p>
                <ul>
                    <li>表格组件没有被正确选中</li>
                    <li>selectedComponent为null或undefined</li>
                    <li>组件选择事件没有正确触发</li>
                </ul>
                <p><strong>解决方案：</strong> 检查组件选择逻辑和事件处理</p>
            </div>

            <div class="scenario">
                <h4>场景2：调试信息显示但组件类型不是'table'</h4>
                <p><strong>可能原因：</strong></p>
                <ul>
                    <li>点击的不是表格组件</li>
                    <li>表格组件的type属性设置错误</li>
                    <li>组件数据结构有问题</li>
                </ul>
                <p><strong>解决方案：</strong> 检查表格组件的定义和类型设置</p>
            </div>

            <div class="scenario">
                <h4>场景3：组件类型是'table'但properties为空</h4>
                <p><strong>可能原因：</strong></p>
                <ul>
                    <li>表格组件的properties对象没有正确初始化</li>
                    <li>数据加载过程中properties丢失</li>
                    <li>组件创建时没有设置默认属性</li>
                </ul>
                <p><strong>解决方案：</strong> 检查组件默认配置和数据初始化</p>
            </div>

            <div class="scenario">
                <h4>场景4：properties存在但rows/cols为undefined</h4>
                <p><strong>可能原因：</strong></p>
                <ul>
                    <li>表格组件的rows和cols属性没有正确设置</li>
                    <li>属性名称不匹配</li>
                    <li>数据类型转换问题</li>
                </ul>
                <p><strong>解决方案：</strong> 检查属性名称和数据类型</p>
            </div>

            <div class="scenario">
                <h4>场景5：调试信息显示正确但输入框仍为空</h4>
                <p><strong>可能原因：</strong></p>
                <ul>
                    <li>Element Plus组件绑定问题</li>
                    <li>Vue响应式系统问题</li>
                    <li>computed属性计算错误</li>
                </ul>
                <p><strong>解决方案：</strong> 检查组件绑定和响应式逻辑</p>
            </div>
        </div>

        <div class="expected-output">
            <h4>✅ 期望的调试输出（表格组件正常情况）</h4>
            <div class="code-snippet">
调试信息：
组件类型: table
组件ID: 3
Properties存在: true
表格行数: 4
表格列数: 3
完整properties: {
  "rows": 4,
  "cols": 3,
  "borderWidth": 1,
  "borderColor": "#000000"
}
            </div>
            <p><strong>如果看到这样的输出，说明数据是正确的，问题在于Element Plus组件绑定。</strong></p>
        </div>

        <div class="warning">
            <h4>⚠️ 如果表格组件不存在</h4>
            <p>如果画布上没有表格组件，请尝试以下方法：</p>
            <ol>
                <li>从左侧组件库拖拽一个表格组件到画布上</li>
                <li>或者刷新页面，确保测试数据正确加载</li>
                <li>检查浏览器控制台是否有JavaScript错误</li>
            </ol>
        </div>

        <div class="test-steps">
            <div class="step-title">📝 请反馈调试结果</div>
            
            <p><strong>请按照上述步骤进行测试，并告诉我：</strong></p>
            <ol>
                <li>是否能看到调试信息框？</li>
                <li>调试信息显示的内容是什么？</li>
                <li>组件类型是否显示为"table"？</li>
                <li>Properties是否存在？</li>
                <li>表格行数和列数显示什么值？</li>
                <li>完整properties的JSON内容是什么？</li>
            </ol>
            
            <p><strong>根据您的反馈，我将能够准确定位问题并提供针对性的解决方案。</strong></p>
        </div>

        <div style="text-align: center; padding: 25px; background: #f8f9fa; border-radius: 8px; margin-top: 30px;">
            <h3>🔍 开始调试测试</h3>
            <p style="font-size: 18px; margin: 15px 0;">
                <strong>测试地址：</strong> 
                <a href="http://localhost:5173" target="_blank" style="color: #FF6B6B; text-decoration: underline;">
                    http://localhost:5173
                </a>
            </p>
            <p><strong>关键步骤：</strong></p>
            <ol style="text-align: left; display: inline-block; margin: 15px 0;">
                <li>点击表格组件</li>
                <li>查看调试信息</li>
                <li>记录显示的内容</li>
                <li>反馈给我进行分析</li>
            </ol>
            <div style="margin-top: 20px; font-size: 14px; color: #666;">
                💡 调试信息将帮助我们快速定位问题的根本原因<br>
                🔧 根据调试结果，我将提供精确的修复方案
            </div>
        </div>
    </div>
</body>
</html>
