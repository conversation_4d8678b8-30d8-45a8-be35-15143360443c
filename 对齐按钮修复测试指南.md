# 对齐按钮修复测试指南

## 测试环境
- 开发服务器：http://localhost:5174
- 浏览器：建议使用 Chrome 或 Firefox 并打开开发者工具

## 修复内容概述

已修复以下问题：
1. ✅ 对齐按钮点击后文本组件消失的问题
2. ✅ 默认值不一致导致的样式异常
3. ✅ 状态初始化逻辑，确保从组件实际属性读取初始状态
4. ✅ 改进了对 undefined 属性的处理

## 详细测试步骤

### 🔍 测试1：基础对齐功能验证

#### 步骤1：创建文本组件
1. 打开 http://localhost:5174
2. 从左侧组件面板拖拽"文本"组件到画布中央
3. 点击选中该文本组件

#### 步骤2：测试水平对齐按钮
1. 在右侧属性面板找到"对齐"部分
2. 依次点击以下按钮，每次点击后观察：
   - **左对齐按钮** (≡) - 验证组件保持可见，按钮变为激活状态
   - **居中对齐按钮** (≣) - 验证组件保持可见，按钮变为激活状态
   - **右对齐按钮** (≡) - 验证组件保持可见，按钮变为激活状态
   - **两端对齐按钮** (≣) - 验证组件保持可见，按钮变为激活状态

**✅ 预期结果**：每次点击后，文本组件应该保持在画布上可见，不会消失或变得透明。

#### 步骤3：测试垂直对齐按钮
1. 继续在"对齐"部分，依次点击：
   - **顶部对齐按钮** (⤴) - 验证组件保持可见
   - **垂直居中按钮** (↕) - 验证组件保持可见
   - **底部对齐按钮** (⤵) - 验证组件保持可见

**✅ 预期结果**：所有垂直对齐按钮点击后，组件都应该保持可见。

### 🔍 测试2：状态保存和恢复验证

#### 步骤1：设置第一个组件的对齐
1. 选中第一个文本组件
2. 点击"居中对齐"按钮
3. 点击"垂直居中"按钮
4. 验证两个按钮都处于激活状态

#### 步骤2：创建第二个组件
1. 拖拽创建第二个文本组件
2. 选中新组件
3. **验证点**：确认对齐按钮显示默认状态（左对齐 + 垂直居中）

#### 步骤3：验证状态恢复
1. 重新点击选中第一个文本组件
2. **关键验证点**：确认"居中对齐"和"垂直居中"按钮仍然处于激活状态
3. 再次选中第二个组件，确认显示默认状态

**✅ 预期结果**：每个组件的对齐状态应该独立保存和正确恢复。

### 🔍 测试3：多组件独立性验证

#### 步骤1：创建多个组件并设置不同对齐
1. 创建3个文本组件
2. 为每个组件设置不同的对齐方式：
   - **组件1**：左对齐 + 顶部对齐
   - **组件2**：居中对齐 + 垂直居中
   - **组件3**：右对齐 + 底部对齐

#### 步骤2：验证独立性
1. 依次点击选中每个组件
2. **验证点**：确认每个组件的对齐按钮状态正确显示对应的设置
3. 重复选择几次，确认状态稳定

**✅ 预期结果**：每个组件的对齐设置应该独立存储，互不影响。

### 🔍 测试4：边界情况验证

#### 步骤1：快速切换测试
1. 选中一个文本组件
2. 快速连续点击不同的对齐按钮
3. **验证点**：组件始终保持可见，不会出现闪烁或消失

#### 步骤2：组合对齐测试
1. 同时设置水平和垂直对齐
2. 尝试各种组合（如：右对齐+顶部对齐，居中+底部对齐等）
3. **验证点**：所有组合都应该正常工作，组件保持可见

#### 步骤3：页面刷新测试
1. 设置几个组件的对齐方式
2. 刷新浏览器页面
3. **验证点**：页面重新加载后，组件应该正常显示（注意：状态存储是临时的，刷新后会重置）

## 🐛 问题排查指南

### 如果组件仍然消失：

1. **检查浏览器控制台**：
   - 按F12打开开发者工具
   - 查看Console标签是否有错误信息
   - 查看Network标签确认资源加载正常

2. **检查组件状态**：
   - 在Console中输入：`document.querySelectorAll('.canvas-component')`
   - 确认组件DOM元素存在
   - 检查元素的style属性是否有异常

3. **检查Vue状态**：
   - 安装Vue DevTools扩展
   - 查看PropertyPanel组件的`currentTextProperties`状态
   - 确认对齐属性值正确

### 如果按钮状态不正确：

1. **检查数据绑定**：
   - 确认`currentTextProperties.textAlign`和`currentTextProperties.verticalAlign`的值
   - 验证按钮的`:class`绑定是否正确

2. **检查状态同步**：
   - 验证`updateTextComponentProperty`函数是否被正确调用
   - 检查`textComponentStates` Map中的数据

## ✅ 成功标准

测试通过的标准：

1. **基础功能**：
   - ✅ 所有对齐按钮点击后组件保持可见
   - ✅ 按钮状态正确切换（激活/非激活）
   - ✅ 无JavaScript错误

2. **状态管理**：
   - ✅ 每个组件的对齐状态独立保存
   - ✅ 切换组件时状态正确恢复
   - ✅ 多个组件的设置互不干扰

3. **用户体验**：
   - ✅ 操作响应迅速，无卡顿
   - ✅ 视觉反馈清晰（按钮状态变化）
   - ✅ 功能行为符合预期

## 📝 测试报告模板

测试完成后，请记录：

```
测试时间：[填写时间]
测试浏览器：[Chrome/Firefox/Safari等]
测试结果：
- 基础对齐功能：✅/❌
- 状态保存恢复：✅/❌  
- 多组件独立性：✅/❌
- 边界情况处理：✅/❌

发现的问题：
[如有问题，请详细描述]

总体评价：
[通过/需要进一步修复]
```

## 🚀 下一步

如果所有测试都通过，说明对齐按钮的修复已经成功。您可以继续测试其他文本属性的解耦功能，或者开始使用新的属性状态管理系统。
