# 对齐按钮修复 - 快速测试指南

## 🚀 立即测试

### 测试地址
http://localhost:5174

### 🔍 核心测试步骤

#### 1. 基础测试（2分钟）
```
1. 打开应用
2. 拖拽"文本"组件到画布
3. 选中文本组件
4. 依次点击右侧属性面板中的对齐按钮：
   - 左对齐 (≡)
   - 居中对齐 (≣) 
   - 右对齐 (≡)
   - 两端对齐 (≣)
   - 顶部对齐 (⤴)
   - 垂直居中 (↕)
   - 底部对齐 (⤵)

✅ 预期结果：每次点击后，文本组件保持可见，按钮状态切换
❌ 如果失败：组件消失或变得不可见
```

#### 2. 状态测试（1分钟）
```
1. 设置第一个组件为"居中对齐"
2. 创建第二个文本组件
3. 切换选择两个组件

✅ 预期结果：每个组件的对齐状态独立保存
❌ 如果失败：状态混乱或不正确
```

## 🔧 修复内容

### 问题根因
- `updateTextComponentProperty` 只更新状态存储，不更新组件数据
- `TextComponent.vue` 从组件数据读取对齐属性
- 数据流断裂导致组件消失

### 修复方案
```javascript
// PropertyPanel.vue - updateTextComponentProperty 函数
const updateTextComponentProperty = (property, value) => {
  if (props.selectedComponent && props.selectedComponent.type === 'text') {
    // 1. 更新状态存储
    currentTextProperties.value[property] = value
    saveTextComponentState(props.selectedComponent.id, currentTextProperties.value)

    // 2. 对于对齐属性，同时更新组件数据（保持可见性）
    if (property === 'textAlign' || property === 'verticalAlign') {
      const newProperties = { ...props.selectedComponent.properties }
      newProperties[property] = value
      emit('property-change', 'properties', newProperties)
    }
  }
}
```

## 🐛 如果问题仍然存在

### 调试步骤

1. **检查浏览器控制台**
   - 按 F12 打开开发者工具
   - 查看 Console 是否有错误信息

2. **检查组件是否存在**
   ```javascript
   // 在控制台执行
   document.querySelectorAll('.canvas-component').length
   ```

3. **检查组件样式**
   ```javascript
   // 在控制台执行
   const comp = document.querySelector('.canvas-component')
   console.log({
     display: comp.style.display,
     opacity: comp.style.opacity,
     visibility: comp.style.visibility
   })
   ```

4. **检查Vue状态**
   - 安装 Vue DevTools 扩展
   - 查看 PropertyPanel 组件的 `currentTextProperties`
   - 查看 `selectedComponent.properties`

### 常见问题

**Q: 组件仍然消失**
A: 检查是否有其他CSS样式冲突，或者组件的尺寸是否为0

**Q: 按钮状态不正确**
A: 检查 `currentTextProperties` 的值是否正确更新

**Q: 状态不保存**
A: 检查 `textComponentStates` Map 是否正确存储数据

## 📝 测试报告

请在测试后填写：

```
测试时间：___________
浏览器：___________

基础对齐功能：✅ / ❌
状态保存恢复：✅ / ❌
多组件独立性：✅ / ❌

问题描述（如有）：
_________________________
_________________________

总体评价：通过 / 需要进一步修复
```

## 🎯 成功标准

- ✅ 所有对齐按钮点击后组件保持可见
- ✅ 按钮状态正确切换
- ✅ 每个组件的对齐状态独立管理
- ✅ 无JavaScript错误
- ✅ 用户体验流畅

如果以上标准都达到，说明修复成功！
