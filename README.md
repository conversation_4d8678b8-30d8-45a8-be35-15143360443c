# OFD模板设计器

一个基于Vue 3的可视化OFD文档模板设计器，支持拖拽式组件设计和实时预览。

## 功能特性

- 🎨 **可视化设计**: 拖拽式组件设计界面
- 📝 **丰富组件**: 支持文本、图片、形状、表格等多种组件
- ⚙️ **属性编辑**: 实时属性面板，支持样式和内容编辑
- 🔍 **缩放预览**: 支持画布缩放和实时预览
- 💾 **模板管理**: 支持模板保存和加载（开发中）
- 📱 **响应式**: 适配不同屏幕尺寸

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite 5.x
- **开发工具**: Vue DevTools
- **样式**: CSS3 + CSS变量（支持暗色模式）

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:5173 查看应用

### 生产构建

```bash
npm run build
```

## 使用说明

1. **组件库**: 左侧面板包含可拖拽的组件
2. **设计画布**: 中间区域为A4尺寸的设计画布
3. **属性面板**: 右侧面板用于编辑选中组件的属性
4. **工具栏**: 顶部包含保存、加载、预览等功能

### 基本操作

- **添加组件**: 从左侧组件库拖拽组件到画布
- **选择组件**: 点击画布上的组件进行选择
- **移动组件**: 拖拽选中的组件改变位置
- **调整大小**: 拖拽选中组件的控制点调整尺寸
- **编辑属性**: 在右侧属性面板修改组件属性
- **删除组件**: 点击选中组件右上角的删除按钮

## 项目结构

```
src/
├── components/
│   ├── designer/           # 设计器核心组件
│   │   ├── DesignerCanvas.vue    # 设计画布
│   │   ├── ComponentLibrary.vue  # 组件库面板
│   │   ├── PropertyPanel.vue     # 属性配置面板
│   │   └── ToolBar.vue          # 工具栏
│   └── ofd-components/     # OFD文档组件
│       ├── TextComponent.vue     # 文本组件
│       ├── ImageComponent.vue    # 图片组件
│       ├── TableComponent.vue    # 表格组件
│       └── ShapeComponent.vue    # 形状组件
├── views/                 # 页面视图
│   └── DesignerView.vue   # 主设计器页面
├── utils/                 # 工具函数
│   └── test-data.js       # 测试数据和工具函数
└── assets/               # 静态资源
    ├── base.css          # 基础样式
    └── main.css          # 主样式文件
```

## 开发计划

- [ ] 完善组件属性编辑
- [ ] 添加更多组件类型（条形码、二维码等）
- [ ] 实现模板保存和加载功能
- [ ] 添加撤销/重做功能
- [ ] 支持组件层级管理
- [ ] 添加网格对齐功能
- [ ] 实现OFD文件导出

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
