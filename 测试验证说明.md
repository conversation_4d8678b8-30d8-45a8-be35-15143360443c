# 文本组件属性解耦功能测试说明

## 测试环境
- 开发服务器：http://localhost:5174
- 测试浏览器：建议使用 Chrome 或 Firefox

## 测试步骤

### 1. 基础功能测试

#### 步骤1：创建文本组件
1. 打开应用 http://localhost:5174
2. 从左侧组件面板拖拽"文本"组件到画布上
3. 确认文本组件成功创建并显示默认文本

#### 步骤2：验证属性面板显示
1. 点击选中刚创建的文本组件
2. 查看右侧属性面板是否显示文本组件的属性
3. 确认显示以下属性控件：
   - 字体选择下拉框（默认：宋体）
   - 字号输入框（默认：14px）
   - 倍数输入框（默认：1）
   - 行间距输入框（默认：1.4）
   - 字间距输入框（默认：0px）
   - 字体样式按钮（粗体、斜体、下划线）
   - 对齐方式按钮组
   - 换行处理下拉框
   - 西文换行复选框
   - 大写数字复选框

### 2. 属性状态存储测试

#### 步骤3：修改第一个文本组件属性
1. 选中第一个文本组件
2. 修改以下属性：
   - 字体：改为"微软雅黑"
   - 字号：改为18
   - 点击"粗体"按钮使其激活
   - 点击"居中对齐"按钮
3. **重要验证点**：确认画布上的文本组件外观没有发生变化
4. 确认属性面板中的控件状态已更新（显示新的设置值）

#### 步骤4：创建第二个文本组件
1. 再次从组件面板拖拽一个新的文本组件到画布
2. 点击选中新的文本组件
3. **验证点**：确认属性面板显示默认值：
   - 字体：宋体
   - 字号：14
   - 粗体按钮：未激活
   - 对齐：左对齐

#### 步骤5：验证属性状态恢复
1. 点击选中第一个文本组件
2. **关键验证点**：确认属性面板恢复显示之前设置的值：
   - 字体：微软雅黑
   - 字号：18
   - 粗体按钮：激活状态
   - 对齐：居中对齐
3. 再次点击第二个文本组件，确认显示默认值
4. 重复切换几次，确认每个组件的属性状态都能正确保持和恢复

### 3. 多组件属性独立性测试

#### 步骤6：配置多个文本组件
1. 创建3个文本组件
2. 为每个组件设置不同的属性：
   
   **组件1**：
   - 字体：微软雅黑
   - 字号：16
   - 粗体：开启
   - 对齐：左对齐
   
   **组件2**：
   - 字体：黑体
   - 字号：20
   - 斜体：开启
   - 对齐：居中
   
   **组件3**：
   - 字体：Arial
   - 字号：12
   - 下划线：开启
   - 对齐：右对齐

3. **验证点**：依次点击每个组件，确认属性面板正确显示对应的配置

### 4. 文本内容编辑测试

#### 步骤7：验证文本内容正常工作
1. 选中任意文本组件
2. 在属性面板的文本内容区域输入新内容
3. **验证点**：确认画布上的文本内容立即更新（这个功能应该保持正常）
4. 切换到其他组件再切换回来，确认文本内容保持

### 5. 基础属性测试

#### 步骤8：验证基础属性仍然正常
1. 选中文本组件
2. 修改基础属性：
   - 位置（x, y坐标）
   - 尺寸（宽度、高度）
   - 旋转角度
   - 透明度
3. **验证点**：确认这些修改立即反映到画布上（基础属性应该保持原有行为）

## 预期结果

### ✅ 正确行为
1. 文本样式属性（字体、字号、样式等）的修改不会立即影响画布显示
2. 每个文本组件的属性状态独立存储和恢复
3. 切换组件时属性面板正确显示对应组件的配置
4. 文本内容编辑仍然正常工作
5. 基础属性（位置、尺寸等）仍然立即影响画布

### ❌ 需要修复的问题
如果出现以下情况，说明需要进一步调试：
1. 属性修改后画布立即发生变化
2. 切换组件时属性面板显示错误的值
3. 属性状态没有正确保存或恢复
4. 文本内容编辑失效
5. 基础属性修改不生效

## 调试提示

如果测试中发现问题：
1. 打开浏览器开发者工具查看控制台错误
2. 检查 Vue DevTools 中的组件状态
3. 确认 `currentTextProperties` 和 `textComponentStates` 的值是否正确
4. 验证事件处理函数是否被正确调用

## 测试完成标准

当以上所有测试步骤都通过，且行为符合预期结果时，说明文本组件属性解耦功能已成功实现。
