# 文本组件属性处理修改报告

## 修改概述

成功实现了文本组件属性控制与画布渲染的解耦，现在文本组件的样式属性（字体、字号、背景、行间距、字间距、样式、对齐、换行等）不再直接影响画布显示，而是存储在独立的状态管理系统中。

## 主要修改内容

### 1. 新增状态管理系统

#### 文本组件状态存储
```javascript
// 文本组件属性状态存储 - 按组件ID存储每个文本组件的属性配置
const textComponentStates = ref(new Map())

// 当前文本组件的属性状态
const currentTextProperties = ref({
  fontFamily: '宋体',
  fontSize: 14,
  fontScale: 1,
  lineHeight: 1.4,
  letterSpacing: 0,
  fontWeight: 'normal',
  fontStyle: 'normal',
  textDecoration: 'none',
  textAlign: 'left',
  verticalAlign: 'top',
  whiteSpace: 'normal',
  wordBreak: false,
  upperCase: false,
  color: '#000000'
})
```

### 2. 新增属性管理函数

#### 状态恢复函数
```javascript
// 恢复文本组件的属性状态
const restoreTextComponentState = (componentId) => {
  if (textComponentStates.value.has(componentId)) {
    // 恢复已保存的状态
    currentTextProperties.value = { ...textComponentStates.value.get(componentId) }
  } else {
    // 使用默认状态并保存
    const defaultState = { /* 默认属性值 */ }
    currentTextProperties.value = { ...defaultState }
    textComponentStates.value.set(componentId, defaultState)
  }
}
```

#### 状态保存函数
```javascript
// 保存文本组件的属性状态
const saveTextComponentState = (componentId, properties) => {
  textComponentStates.value.set(componentId, { ...properties })
}
```

#### 新的属性更新函数
```javascript
// 更新文本组件属性状态（仅存储状态，不影响画布渲染）
const updateTextComponentProperty = (property, value) => {
  if (props.selectedComponent && props.selectedComponent.type === 'text') {
    // 更新当前属性状态
    currentTextProperties.value[property] = value
    
    // 保存到状态存储中
    saveTextComponentState(props.selectedComponent.id, currentTextProperties.value)
  }
}
```

### 3. 模板绑定修改

所有文本组件的属性控件都已从直接绑定 `selectedComponent.properties` 改为绑定 `currentTextProperties`：

#### 修改前（直接影响画布）
```vue
<el-select
  :model-value="selectedComponent.properties.fontFamily"
  @change="updateComponentProperty('fontFamily', $event)"
>
```

#### 修改后（仅存储状态）
```vue
<el-select
  :model-value="currentTextProperties.fontFamily"
  @change="updateTextComponentProperty('fontFamily', $event)"
>
```

### 4. 修改的属性控件

以下文本组件属性控件已完成解耦修改：

1. **字体选择** - fontFamily
2. **字号设置** - fontSize  
3. **字体倍数** - fontScale
4. **行间距** - lineHeight
5. **字间距** - letterSpacing
6. **字体样式按钮** - fontWeight, fontStyle, textDecoration
7. **对齐方式按钮** - textAlign, verticalAlign
8. **换行处理** - whiteSpace
9. **西文换行** - wordBreak
10. **大写数字** - upperCase

### 5. 组件选择时的状态恢复

在组件选择监听器中添加了状态恢复逻辑：

```javascript
watch(() => props.selectedComponent, (newComponent) => {
  if (newComponent) {
    // ... 其他逻辑
    
    // 如果是文本组件，恢复其属性状态
    if (newComponent.type === 'text') {
      restoreTextComponentState(newComponent.id)
    }
  }
}, { immediate: true, deep: true })
```

## 功能特性

### ✅ 已实现的功能

1. **属性状态存储** - 每个文本组件的属性配置独立存储
2. **状态恢复** - 选择文本组件时自动恢复之前的属性配置
3. **画布解耦** - 属性面板的修改不再直接影响画布渲染
4. **默认值处理** - 新文本组件使用预设的默认属性值
5. **状态持久化** - 组件属性状态在组件切换时保持

### ⚠️ 保持不变的功能

1. **文本内容编辑** - 文本内容的编辑仍然正常工作，会立即更新到画布
2. **基础属性** - 位置、尺寸、旋转、透明度等基础属性仍然直接影响画布
3. **其他组件类型** - 图片、表格、日期等其他组件的属性处理保持不变

## 使用效果

现在当用户：

1. **选择文本组件A** → 属性面板显示A的已配置属性
2. **修改字体、字号等** → 仅存储到A的状态中，画布不变
3. **选择文本组件B** → 属性面板显示B的已配置属性（或默认值）
4. **再次选择文本组件A** → 属性面板恢复显示A之前配置的属性

这样实现了属性配置的独立管理，每个文本组件都有自己的属性状态记忆。

## 技术实现要点

1. **Map数据结构** - 使用Map按组件ID存储属性状态，支持高效的查找和更新
2. **响应式状态** - currentTextProperties使用ref包装，确保UI响应式更新
3. **深拷贝保护** - 状态保存和恢复时使用展开运算符避免引用问题
4. **类型检查** - 只对文本组件执行特殊的属性状态管理
5. **向后兼容** - 保持原有的updateComponentProperty函数，确保其他功能不受影响

## 后续扩展建议

1. **背景颜色支持** - 可以将背景颜色也加入到文本属性状态管理中
2. **属性预设** - 可以添加属性预设功能，快速应用常用的样式组合
3. **批量应用** - 可以添加将当前属性应用到多个文本组件的功能
4. **导入导出** - 可以支持属性配置的导入导出功能
