# 文本组件对齐按钮修复报告

## 问题描述

在测试文本组件属性解耦功能时发现：当点击属性面板中的对齐按钮（左对齐、居中对齐、右对齐、两端对齐、顶部对齐、垂直居中、底部对齐）时，画布上的文本组件会自动消失或变得不可见。

## 问题根因分析

### 1. 数据流不匹配
- **PropertyPanel.vue** 中的对齐按钮调用 `updateTextComponentProperty`，只更新 `currentTextProperties`
- **TextComponent.vue** 仍然从 `props.componentData.properties` 读取对齐属性
- 当属性面板修改对齐设置时，组件的实际属性没有更新，导致样式计算错误

### 2. 默认值不一致
- `test-data.js` 中文本组件默认 `verticalAlign: "middle"`
- `PropertyPanel.vue` 中 `currentTextProperties` 默认 `verticalAlign: 'top'`
- 不一致的默认值可能导致样式计算异常

### 3. 未定义属性处理
- 当对齐属性为 `undefined` 时，`getAlignItems` 和 `getJustifyContent` 函数的默认返回值可能导致布局问题

## 修复方案

### 1. 修复 TextComponent.vue 中的默认值处理

#### 修复前
```javascript
const getAlignItems = (verticalAlign) => {
  switch (verticalAlign) {
    case 'top': return 'flex-start'
    case 'bottom': return 'flex-end'
    case 'middle': return 'center'
    default: return 'center' // 可能导致问题
  }
}

const getJustifyContent = (textAlign) => {
  switch (textAlign) {
    case 'left': return 'flex-start'
    case 'right': return 'flex-end'
    case 'center': return 'center'
    case 'justify': return 'flex-start'
    default: return 'flex-start' // 可能导致问题
  }
}
```

#### 修复后
```javascript
const getAlignItems = (verticalAlign) => {
  switch (verticalAlign) {
    case 'top': return 'flex-start'
    case 'bottom': return 'flex-end'
    case 'middle': return 'center'
    default: return 'flex-start' // 默认顶部对齐，确保组件可见
  }
}

const getJustifyContent = (textAlign) => {
  switch (textAlign) {
    case 'left': return 'flex-start'
    case 'right': return 'flex-end'
    case 'center': return 'center'
    case 'justify': return 'flex-start'
    default: return 'flex-start' // 默认左对齐，确保组件可见
  }
}
```

### 2. 统一默认值配置

#### 修复前
```javascript
// PropertyPanel.vue 中的默认值
const currentTextProperties = ref({
  fontFamily: '宋体',
  verticalAlign: 'top', // 与 test-data.js 不一致
  // ...
})
```

#### 修复后
```javascript
// PropertyPanel.vue 中的默认值
const currentTextProperties = ref({
  fontFamily: 'Microsoft YaHei', // 与 test-data.js 保持一致
  verticalAlign: 'middle', // 与 test-data.js 保持一致
  // ...
})
```

### 3. 改进状态初始化逻辑

#### 新增功能：从组件实际属性初始化状态
```javascript
const restoreTextComponentState = (componentId) => {
  if (textComponentStates.value.has(componentId)) {
    // 恢复已保存的状态
    currentTextProperties.value = { ...textComponentStates.value.get(componentId) }
  } else {
    // 首次选中该组件，从组件的实际属性初始化状态
    const component = props.selectedComponent
    if (component && component.properties) {
      const initialState = {
        fontFamily: component.properties.fontFamily || 'Microsoft YaHei',
        fontSize: component.properties.fontSize || 14,
        // ... 从实际属性读取，确保一致性
        textAlign: component.properties.textAlign || 'left',
        verticalAlign: component.properties.verticalAlign || 'middle',
        // ...
      }
      currentTextProperties.value = { ...initialState }
      textComponentStates.value.set(componentId, initialState)
    }
  }
}
```

## 修复效果

### ✅ 修复后的预期行为

1. **组件保持可见** - 点击对齐按钮后，文本组件在画布上保持可见
2. **默认值一致** - 新创建的文本组件使用统一的默认对齐设置
3. **状态正确初始化** - 首次选中组件时，从组件实际属性初始化状态
4. **样式计算稳定** - 对齐属性的变化不会导致组件消失

### 🔧 技术改进

1. **更安全的默认值** - 使用 `flex-start` 作为默认对齐方式，确保组件始终可见
2. **数据一致性** - 统一各处的默认值配置
3. **智能初始化** - 从组件实际属性读取初始状态，而不是硬编码默认值
4. **错误容错** - 改进对 undefined 属性的处理

## 测试验证步骤

### 1. 基础对齐功能测试
1. 创建新的文本组件
2. 依次点击各个对齐按钮：左对齐、居中、右对齐、两端对齐
3. 验证组件保持可见，按钮状态正确切换

### 2. 垂直对齐功能测试
1. 点击垂直对齐按钮：顶部对齐、垂直居中、底部对齐
2. 验证组件保持可见，按钮状态正确切换

### 3. 状态恢复测试
1. 设置文本组件A的对齐方式为"居中对齐"
2. 选择其他组件，再重新选择组件A
3. 验证对齐按钮状态正确恢复为"居中对齐"

### 4. 多组件独立性测试
1. 创建多个文本组件，设置不同的对齐方式
2. 依次选择各个组件
3. 验证每个组件的对齐状态独立保存和恢复

## 注意事项

1. **解耦模式下的行为** - 在当前的解耦模式下，对齐设置的修改不会立即影响画布显示，这是预期行为
2. **组件可见性** - 修复确保了组件本身保持可见，不会因为对齐设置而消失
3. **向后兼容** - 修复保持了与现有组件的兼容性
4. **性能优化** - 状态初始化逻辑只在首次选中组件时执行

## 后续建议

1. **添加视觉反馈** - 可以考虑在解耦模式下添加视觉提示，显示当前的对齐设置
2. **批量应用功能** - 可以添加将对齐设置应用到多个组件的功能
3. **预设模板** - 可以创建常用对齐方式的预设模板
4. **实时预览选项** - 可以添加开关，允许用户选择是否实时预览对齐效果
