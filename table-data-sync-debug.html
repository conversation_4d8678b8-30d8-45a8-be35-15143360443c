<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格组件数据同步调试报告</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 8px;
        }
        .debug-section {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            color: #0d47a1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .step-list {
            list-style: none;
            padding: 0;
            counter-reset: step-counter;
        }
        .step-list li {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
            counter-increment: step-counter;
            position: relative;
            padding-left: 50px;
        }
        .step-list li:before {
            content: "步骤" counter(step-counter);
            position: absolute;
            left: 0;
            top: 15px;
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 11px;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .data-flow {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .flow-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .flow-arrow {
            text-align: center;
            font-size: 24px;
            color: #667eea;
            margin: 10px 0;
        }
        .expected-result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 表格组件数据同步调试</h1>
            <p>检查PropertyPanel.vue中表格行数列数的数据回显问题</p>
        </div>

        <div class="debug-section">
            <h3>🎯 问题分析与修复</h3>
            <p><strong>发现的问题：</strong> 测试数据中缺少表格组件，导致无法测试表格属性面板功能</p>
            <p><strong>已完成修复：</strong></p>
            <ul>
                <li>✅ 在sampleTemplate中添加了表格组件测试数据</li>
                <li>✅ 在PropertyPanel中添加了调试信息显示</li>
                <li>✅ 验证了TableComponent的正确实现</li>
                <li>✅ 确认了ComponentLibrary中表格组件的存在</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">📋 测试步骤</div>
            
            <ol class="step-list">
                <li>
                    <strong>访问应用</strong><br>
                    打开 <a href="http://localhost:5173" target="_blank">http://localhost:5173</a>
                </li>
                <li>
                    <strong>查看画布</strong><br>
                    应该能看到画布上有一个表格组件（位置：x=400, y=50）
                </li>
                <li>
                    <strong>点击表格组件</strong><br>
                    点击画布上的表格组件，使其被选中（应该有选中边框）
                </li>
                <li>
                    <strong>检查属性面板</strong><br>
                    查看右侧PropertyPanel中是否显示表格组件的属性
                </li>
                <li>
                    <strong>查看调试信息</strong><br>
                    在表格组件属性区域应该能看到灰色背景的调试信息框
                </li>
                <li>
                    <strong>验证数据显示</strong><br>
                    检查行数和列数输入框是否显示正确的数值（应该是4行3列）
                </li>
                <li>
                    <strong>测试编辑功能</strong><br>
                    尝试修改行数和列数，观察画布上的表格是否实时更新
                </li>
            </ol>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 添加的调试信息</div>
            
            <p>在PropertyPanel.vue的表格组件属性区域添加了调试信息显示：</p>
            <div class="code-snippet">
&lt;!-- 调试信息 --&gt;
&lt;div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 12px;"&gt;
  &lt;strong&gt;调试信息：&lt;/strong&gt;&lt;br&gt;
  组件类型: {{ selectedComponent.type }}&lt;br&gt;
  行数值: {{ selectedComponent.properties?.rows }}&lt;br&gt;
  列数值: {{ selectedComponent.properties?.cols }}&lt;br&gt;
  完整properties: {{ JSON.stringify(selectedComponent.properties, null, 2) }}
&lt;/div&gt;
            </div>
            
            <p><strong>这个调试信息将显示：</strong></p>
            <ul>
                <li>当前选中组件的类型</li>
                <li>表格的行数值</li>
                <li>表格的列数值</li>
                <li>完整的properties对象内容</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">📊 数据流验证</div>
            
            <div class="data-flow">
                <div class="flow-item">
                    <h4>test-data.js</h4>
                    <p>表格组件定义</p>
                    <div class="code-snippet">
{
  id: "3",
  type: "table",
  x: 400,
  y: 50,
  width: 200,
  height: 120,
  properties: {
    rows: 4,
    cols: 3,
    borderWidth: 1,
    borderColor: "#000000"
  }
}
                    </div>
                </div>
                
                <div class="flow-item">
                    <h4>DesignerView.vue</h4>
                    <p>组件选择处理</p>
                    <div class="code-snippet">
selectedComponent.value = component
                    </div>
                </div>
                
                <div class="flow-item">
                    <h4>PropertyPanel.vue</h4>
                    <p>属性显示</p>
                    <div class="code-snippet">
:model-value="selectedComponent.properties.rows"
:model-value="selectedComponent.properties.cols"
                    </div>
                </div>
            </div>
            
            <div class="flow-arrow">↓ 数据流向 ↓</div>
            
            <div class="expected-result">
                <h4>✅ 预期结果</h4>
                <ul>
                    <li>调试信息显示：组件类型 = "table"</li>
                    <li>调试信息显示：行数值 = 4</li>
                    <li>调试信息显示：列数值 = 3</li>
                    <li>行数输入框显示：4</li>
                    <li>列数输入框显示：3</li>
                    <li>画布上显示4行3列的表格</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🚨 可能的问题排查</div>
            
            <div class="warning">
                <h4>如果调试信息显示undefined或null：</h4>
                <ul>
                    <li>检查selectedComponent是否正确传递</li>
                    <li>检查组件选择事件是否正常触发</li>
                    <li>验证表格组件的数据结构是否完整</li>
                </ul>
            </div>
            
            <div class="warning">
                <h4>如果输入框显示为空：</h4>
                <ul>
                    <li>检查:model-value绑定路径是否正确</li>
                    <li>验证selectedComponent.properties是否存在</li>
                    <li>确认rows和cols属性是否正确设置</li>
                </ul>
            </div>
            
            <div class="warning">
                <h4>如果修改后不生效：</h4>
                <ul>
                    <li>检查@change事件是否正确触发</li>
                    <li>验证updateComponentProperty函数是否正常工作</li>
                    <li>确认DesignerView中的handlePropertyChange是否正确处理</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔄 如果问题仍然存在</div>
            
            <p><strong>请按以下步骤进行进一步调试：</strong></p>
            
            <ol>
                <li><strong>检查浏览器控制台</strong> - 查看是否有JavaScript错误</li>
                <li><strong>查看调试信息</strong> - 确认selectedComponent的数据结构</li>
                <li><strong>验证组件选择</strong> - 确认点击表格组件时是否正确选中</li>
                <li><strong>测试其他组件</strong> - 检查文本组件的属性是否正常显示</li>
                <li><strong>检查网络请求</strong> - 确认没有数据加载问题</li>
            </ol>
            
            <p><strong>如果调试信息显示正确但输入框仍为空，可能需要：</strong></p>
            <ul>
                <li>检查Element Plus版本兼容性</li>
                <li>验证Vue 3的响应式系统是否正常工作</li>
                <li>确认组件的生命周期是否正确</li>
            </ul>
        </div>

        <div style="text-align: center; padding: 25px; background: #e3f2fd; border-radius: 8px; margin-top: 30px; border: 1px solid #bbdefb;">
            <h3>🔍 开始调试测试</h3>
            <p style="font-size: 18px; margin: 15px 0;">
                <strong>测试地址：</strong> 
                <a href="http://localhost:5173" target="_blank" style="color: #1976d2; text-decoration: underline;">
                    http://localhost:5173
                </a>
            </p>
            <p><strong>关键检查点：</strong></p>
            <ol style="text-align: left; display: inline-block; margin: 15px 0;">
                <li>画布上是否有表格组件</li>
                <li>点击表格组件是否能选中</li>
                <li>属性面板是否显示调试信息</li>
                <li>调试信息中的数据是否正确</li>
                <li>行数列数输入框是否显示数值</li>
            </ol>
            <div style="margin-top: 20px; font-size: 14px; color: #666;">
                💡 如果看到调试信息显示正确的数据，说明数据流是正常的<br>
                🔧 如果输入框仍为空，则是Element Plus组件绑定的问题
            </div>
        </div>
    </div>
</body>
</html>
