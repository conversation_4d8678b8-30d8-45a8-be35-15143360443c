# 二维码组件UI调整测试指南

## 测试环境
- 开发服务器：http://localhost:5174
- 测试页面：设计器主页面

## 测试步骤

### 1. 标签对齐测试
1. 访问 http://localhost:5174
2. 进入设计器页面
3. 从组件库中拖拽添加一个二维码组件到画布
4. 点击选中二维码组件
5. 查看右侧属性面板

**验证点：**
- ✅ "ID"标签是否与"组件属性"标题中的"组"字左对齐
- ✅ "类型"标签是否与"组件属性"标题中的"组"字左对齐
- ✅ 两个标签是否与其他组件的属性标签保持一致的对齐方式

### 2. 下拉框默认显示测试
1. 继续在属性面板中查看"类型"下拉选择框
2. 观察下拉框的初始显示状态

**验证点：**
- ✅ 下拉框是否显示"请选择"而不是"请选择二维码类型"
- ✅ 下拉框是否没有预选任何选项（不显示"快速响应矩阵码（QR码）"）
- ✅ 点击下拉框是否能正常展开选项列表

### 3. 交互功能测试
1. 在"ID"输入框中输入测试ID，如："QR001"
2. 点击"类型"下拉框，选择"快速响应矩阵码（QR码）"
3. 切换选择其他组件，再重新选择二维码组件

**验证点：**
- ✅ ID值是否正确保存和显示
- ✅ 类型选择是否正确保存和显示
- ✅ 表单验证是否正常工作（尝试清空必填字段）

### 4. UI一致性测试
1. 选择其他类型的组件（如文本组件、图片组件等）
2. 对比属性面板的标签对齐和样式

**验证点：**
- ✅ 二维码组件的标签对齐是否与其他组件保持一致
- ✅ 输入框和下拉框的样式是否与其他组件保持一致
- ✅ 整体视觉效果是否和谐统一

## 预期结果

### 标签对齐效果
```
组件属性
├─ ID        [输入框]
├─ 类型      [下拉框: 请选择 ▼]
```

### 下拉框选项
- 快速响应矩阵码（QR码）
- 网格矩阵码（GM码）

### 初始状态
- ID输入框：空白，显示placeholder
- 类型下拉框：显示"请选择"，无预选项

## 问题排查

如果发现问题，请检查：

1. **标签对齐问题**
   - 检查`el-form`的`label-width="80px"`设置
   - 确认`el-form-item`的label属性正确

2. **下拉框显示问题**
   - 检查`:model-value`绑定是否移除了默认值
   - 确认`placeholder="请选择"`设置正确

3. **功能异常**
   - 查看浏览器控制台是否有错误信息
   - 检查`updateComponentDataProperty`方法调用

## UI修复更新 (最新)

### 🔧 标签对齐问题修复
**问题**：二维码组件使用`el-form-item`导致标签对齐不一致
**解决方案**：改用与其他组件一致的`property-grid`布局系统

**修改前**：
```html
<el-form-item label="ID">
<el-form-item label="类型">
```

**修改后**：
```html
<div class="property-group">
  <div class="property-grid">
    <div class="property-item-full">
      <label class="property-label">ID</label>
    <div class="property-item-full">
      <label class="property-label">类型</label>
```

### 🔧 下拉框弹出位置修复
**问题**：下拉选择框在侧边弹出而不是正下方
**解决方案**：添加专门的CSS样式修复定位问题

**关键修复样式**：
```css
/* 修复下拉框弹出位置 */
.qrcode-select-dropdown {
  z-index: 9999 !important;
}

.property-panel :deep(.el-select-dropdown) {
  z-index: 9999 !important;
  position: fixed !important;
}

/* 确保容器不裁剪下拉框 */
.property-panel .property-group {
  overflow: visible !important;
}
```

### ✅ 修复效果验证
- **标签对齐**：ID和类型标签现在与"组件属性"标题完全左对齐
- **下拉框位置**：点击类型下拉框时，选项列表在正下方弹出
- **布局一致性**：与其他组件属性面板保持完全一致的布局

## 技术细节

### 关键修改点
```javascript
// 布局系统改为property-grid
<div class="property-group">
  <div class="property-grid">
    <div class="property-item-full">

// 下拉框添加特殊类名
class="property-select-full"
popper-class="qrcode-select-dropdown"
```

### 对齐机制
- 使用`property-grid`布局系统替代`el-form`
- 通过`property-label`类统一标签样式
- 与其他组件属性保持完全一致的布局结构
